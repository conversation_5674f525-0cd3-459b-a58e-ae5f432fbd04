import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:vcc/generated/l10n.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/routers/router_config.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      theme: ThemeData(
        appBarTheme: AppBarTheme(
          backgroundColor: BaseColors.backgroundWhite,
          scrolledUnderElevation: 0.0,
        ),
      ),
      builder: (context, child) {
        final MediaQueryData data = MediaQuery.of(context);
        return MediaQuery(
          data: data.copyWith(
            textScaler: TextScaler.noScaling,
          ),
          child: Navigator(
            key: ErrorDialog.navigatorKey,
            onGenerateRoute: (RouteSettings settings) {
              return MaterialPageRoute(
                builder: (context) => child!,
              );
            },
          ),
        );
      },
      locale: const Locale('vi'),
      debugShowCheckedModeBanner: false,
      routerConfig: goRouterConfiguration,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        AppStrings.delegate,
      ],
      supportedLocales: AppStrings.supportedLocales,
    );
  }
}
