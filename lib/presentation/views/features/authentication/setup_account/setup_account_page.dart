import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:vcc/domain/enums/order_agency/enum_setup_account_industry_type.dart';
import 'package:vcc/generated/l10n.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/send_otp_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/domain/entities/address_info.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/domain/body/register/sys_user_dto.dart';
import 'package:vcc/domain/body/register/register_body.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/domain/body/register/register_image.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/enums/register/profession_enum.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/domain/enums/register/category_register_enum.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/domain/body/register/aio_config_level_code_dto.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_complain_widget.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker.dart';
import 'package:vcc/presentation/views/features/authentication/setup_account/setup_account_view_model.dart';
import 'package:vcc/presentation/views/features/authentication/setup_account/widget/build_image_information.dart';
import 'package:vcc/presentation/views/features/authentication/sign_contract_register/sign_contract_register.dart';
import 'package:vcc/presentation/views/features/authentication/setup_account/widget/build_registration_information.dart';
import 'package:vcc/presentation/views/features/authentication/setup_account/widget/select_profession/select_profession.dart';

class SetupAccountArguments {
  final bool isDetail;
  final int? sysUserId;
  final Function? onCompletedDetail;

  SetupAccountArguments({
    this.isDetail = false,
    this.sysUserId,
    this.onCompletedDetail,
  });
}

class SetupAccountPage extends StatefulHookConsumerWidget {
  final SetupAccountArguments arguments;

  const SetupAccountPage({
    super.key,
    required this.arguments,
  });

  @override
  ConsumerState<SetupAccountPage> createState() => _SetupAccountPageState();
}

class _SetupAccountPageState extends ConsumerState<SetupAccountPage> {
  final formProcessKey = GlobalKey<FormState>();

  late TextEditingController nameController;
  late TextEditingController phoneController;
  late TextEditingController phoneVTController;
  late TextEditingController numberCCCDController;
  late TextEditingController taxCodeController;
  late TextEditingController bankAccountNumberController;
  late TextEditingController bankBranchController;

  late TextEditingController fullNameRepresentativeController;
  late TextEditingController positionRepresentativeController;
  late TextEditingController numberCCCDRepresentativeController;
  late TextEditingController phoneRepresentativeController;
  late TextEditingController nationalityRepresentativeController;

  late TextEditingController passwordController;
  late TextEditingController confirmPasswordController;

  late TextEditingController fullNameReferrerController;
  late TextEditingController phoneReferrerController;
  late TextEditingController referralCodeController;

  @override
  void initState() {
    initController();
    Future(() async {
      await ref.read(setupAccountProvider.notifier).onRefresh(
            isDetail: widget.arguments.isDetail,
            sysUserId: widget.arguments.sysUserId,
          );
      var state = ref.watch(setupAccountProvider);
      taxCodeController.text = state.detailCollaboratorData?.taxCodeUser ?? '';
      nameController.text = state.detailCollaboratorData?.fullName ?? '';
      phoneReferrerController.text =
          state.detailCollaboratorData?.parentPhoneNumber ?? '';
      numberCCCDController.text = state.detailCollaboratorData?.taxCode ?? '';
      phoneVTController.text =
          state.detailCollaboratorData?.accountNumber ?? '';
      fullNameReferrerController.text =
          state.detailCollaboratorData?.parentFullName ?? '';
      bankBranchController.text =
          state.detailCollaboratorData?.bankBranch ?? '';
      fullNameRepresentativeController.text =
          state.detailCollaboratorData?.representDl ?? '';
      positionRepresentativeController.text =
          state.detailCollaboratorData?.positionDl ?? '';
      bankAccountNumberController.text =
          state.detailCollaboratorData?.agencyAccountNumber ?? '';
      numberCCCDRepresentativeController.text =
          state.detailCollaboratorData?.citizenIdRepresent ?? '';
      phoneRepresentativeController.text =
          state.detailCollaboratorData?.phoneRepresent ?? '';
      referralCodeController.text =
          state.detailCollaboratorData?.referralCode ?? '';
      phoneController.text = state.detailCollaboratorData?.phoneNumber ?? '';
    });
    super.initState();
  }

  Future<void> onRefresh() async {
    ref.read(setupAccountProvider.notifier).onRefresh(
          isDetail: widget.arguments.isDetail,
          sysUserId: widget.arguments.sysUserId,
        );
  }

  void initController() {
    nameController = TextEditingController();
    phoneController = TextEditingController();
    phoneVTController = TextEditingController();
    numberCCCDController = TextEditingController();
    taxCodeController = TextEditingController();
    bankAccountNumberController = TextEditingController();
    bankBranchController = TextEditingController();

    fullNameRepresentativeController = TextEditingController();
    positionRepresentativeController = TextEditingController();
    numberCCCDRepresentativeController = TextEditingController();
    phoneRepresentativeController = TextEditingController();
    nationalityRepresentativeController = TextEditingController();

    passwordController = TextEditingController();
    confirmPasswordController = TextEditingController();

    fullNameReferrerController = TextEditingController();
    phoneReferrerController = TextEditingController();
    referralCodeController = TextEditingController();
  }

  @override
  void dispose() {
    nameController.dispose();
    phoneController.dispose();
    phoneVTController.dispose();
    numberCCCDController.dispose();
    taxCodeController.dispose();
    bankAccountNumberController.dispose();
    bankBranchController.dispose();

    fullNameRepresentativeController.dispose();
    positionRepresentativeController.dispose();
    numberCCCDRepresentativeController.dispose();
    phoneRepresentativeController.dispose();
    nationalityRepresentativeController.dispose();

    passwordController.dispose();
    confirmPasswordController.dispose();

    fullNameReferrerController.dispose();
    phoneReferrerController.dispose();
    referralCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      appBar: AppBarCustom(
        title: AppStrings.of(context).register,
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          color: BaseColors.backgroundWhite,
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
            horizontal: BaseSpacing.spacing4,
          ),
          child: BaseButton(
            text: "Đăng ký",
            onTap: () => onSignContract(context),
          ),
        ),
      ),
      body: body(),
    );
  }

  Widget body() {
    var state = ref.watch(setupAccountProvider);
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }
    if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: onRefresh,
      );
    }
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Form(
        key: formProcessKey,
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Container(
                color: BaseColors.backgroundWhite,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const BuildRegistrationInformation(),
                    const DividerWidget(
                      height: BaseSpacing.spacing2,
                    ),
                    buildEntityInformation(),
                    Visibility(
                      visible: !(state.entityGroup ==
                              CategoryRegisterEnum.hkd &&
                          state.entityType == CategoryRegisterEnum.personal),
                      child: buildRepresentativeInformation(),
                    ),
                    const DividerWidget(
                      height: BaseSpacing.spacing2,
                    ),
                    buildAccountInformation(),
                    const DividerWidget(
                      height: BaseSpacing.spacing2,
                    ),
                    buildOtherInformation(),
                  ],
                ),
              ),
            ),
            if (state.loadStatusUpdate == LoadStatus.loading) ...[
              const Center(
                child: LoadingIndicatorWidget(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget buildEntityInformation() {
    var state = ref.watch(setupAccountProvider);
    return Padding(
      padding: const EdgeInsets.only(
        top: BaseSpacing.spacing4,
        left: BaseSpacing.spacing4,
        right: BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: Text(
              state.entityGroup == CategoryRegisterEnum.hkd
                  ? 'Thông tin hộ kinh doanh'
                  : state.entityGroup == CategoryRegisterEnum.agency
                      ? 'Thông tin đại lý'
                      : 'Thông tin OFT',
              style: UITextStyle.body1SemiBold.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              key: const ValueKey('name'),
              labelText: state.entityType == CategoryRegisterEnum.company
                  ? 'Tên công ty/doanh nghiệp '
                  : state.entityGroup == CategoryRegisterEnum.hkd
                      ? 'Tên HKD'
                      : state.entityGroup == CategoryRegisterEnum.agency
                          ? 'Tên đại lý'
                          : 'Tên OFT',
              isRequired: true,
              controller: nameController,
              keyboardType: TextInputType.text,
              textInputAction: TextInputAction.done,
              validator: (value) {
                return ValidateUtils.onValidateRegister(
                  value: value,
                  title: state.entityType == CategoryRegisterEnum.company
                      ? 'Tên công ty/doanh nghiệp'
                      : state.entityGroup == CategoryRegisterEnum.hkd
                          ? 'Tên HKD'
                          : state.entityGroup == CategoryRegisterEnum.agency
                              ? 'Tên đại lý'
                              : 'Tên OFT',
                );
              },
              onChanged: (value) {
                String formatted = StringUtils.formatRegister(value);
                if (value != formatted) {
                  nameController.value = nameController.value.copyWith(
                    text: formatted,
                    selection:
                        TextSelection.collapsed(offset: formatted.length),
                  );
                }
              },
            ),
          ),
          if (state.entityGroup != CategoryRegisterEnum.agency &&
              state.entityType == CategoryRegisterEnum.personal) ...[
            Padding(
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing4,
              ),
              child: DropdownWidget(
                labelText: "Ngày sinh",
                isRequired: true,
                content: state.dateOfBirth?.displayView(
                  format: DateTimeFormater.dateFormatVi,
                ),
                suffix: MyAssets.icons.iconCalendarS24.svg(),
                onTap: () async {
                  DateTime now = DateTime.now();
                  DateTime maxDate =
                      DateTime(now.year - 16, now.month, now.day);
                  openDatetimePicker(
                    maxDate: maxDate,
                    date: state.dateOfBirth ?? maxDate,
                    onSubmit: (date) {
                      ref
                          .read(setupAccountProvider.notifier)
                          .changeDateOfBirth(date);
                    },
                  );
                },
                validator: (value) {
                  return ValidateUtils.onValidateNotNull(
                    value: value,
                    title: "Ngày sinh",
                  );
                },
              ),
            ),
          ],
          if (state.entityGroup == CategoryRegisterEnum.hkd &&
              state.entityType == CategoryRegisterEnum.company) ...[
            Padding(
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing4,
              ),
              child: DropdownWidget(
                labelText: "Ngày thành lập",
                isRequired: true,
                content: state.dateOfEstablishment?.displayView(
                  format: DateTimeFormater.dateFormatVi,
                ),
                suffix: MyAssets.icons.iconCalendarS24.svg(),
                onTap: () async {
                  DateTime now = DateTime.now();
                  openDatetimePicker(
                    maxDate: now,
                    date: state.dateOfEstablishment ?? now,
                    onSubmit: (date) {
                      ref
                          .read(setupAccountProvider.notifier)
                          .changeDateOfEstablishment(date);
                    },
                  );
                },
                validator: (value) {
                  return ValidateUtils.onValidateNotNull(
                    value: value,
                    title: "Ngày thành lập",
                  );
                },
              ),
            ),
          ],
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              key: const ValueKey('phone'),
              labelText: 'Số điện thoại',
              isRequired: true,
              maxLength: 10,
              controller: phoneController,
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.done,
              validator: (value) {
                return ValidateUtils.onValidatePhone(value);
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: DropdownWidget(
              labelText: "Ngày hiệu lực",
              isRequired: true,
              content: state.effectiveDate?.displayView(
                format: DateTimeFormater.dateFormatVi,
              ),
              suffix: MyAssets.icons.iconCalendarS24.svg(),
              onTap: () async {
                DateTime now = DateTime.now();
                DateTime minDate = DateTime(now.year, now.month, now.day + 1);
                openDatetimePicker(
                  minDate: minDate,
                  date: state.effectiveDate ?? minDate,
                  onSubmit: (date) {
                    ref
                        .read(setupAccountProvider.notifier)
                        .changeEffectiveDate(date);
                  },
                );
              },
              validator: (value) {
                return ValidateUtils.onValidateNotNull(
                  value: value,
                  title: "Ngày hiệu lực",
                );
              },
            ),
          ),
          if (state.entityGroup == CategoryRegisterEnum.hkd ||
              (state.entityGroup == CategoryRegisterEnum.oft &&
                  state.entityType == CategoryRegisterEnum.personal)) ...[
            Padding(
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing4,
              ),
              child: TextFieldWidget(
                key: const ValueKey('phoneVT'),
                labelText: 'Số điện thoại Viettel Money',
                isRequired: true,
                maxLength: 10,
                controller: phoneVTController,
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.done,
                validator: (value) {
                  return ValidateUtils.onValidatePhone(value);
                },
              ),
            ),
          ],
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: DropdownWidget(
              labelText: "Địa chỉ",
              isRequired: true,
              content: state.address?.getFullAddress,
              suffix: MyAssets.icons.iconArrowRightS20.svg(),
              validator: (value) {
                return ValidateUtils.onValidateNotNull(
                  value: value,
                  title: "Địa chỉ",
                );
              },
              onTap: () {
                onSelectAddress(
                  addressInfo: state.address,
                );
              },
            ),
          ),
          if (state.entityGroup == CategoryRegisterEnum.hkd &&
              state.entityType == CategoryRegisterEnum.personal) ...[
            Padding(
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing4,
              ),
              child: DropdownWidget(
                labelText: "Nghề nghiệp",
                isRequired: true,
                content:
                    state.profession?.code == ProfessionEnum.other.keyToServer
                        ? state.profession?.professionName
                        : state.profession?.name,
                suffix: MyAssets.icons.arrowDown.svg(),
                validator: (value) {
                  return ValidateUtils.onValidateNotNull(
                    value: value,
                    title: "Nghề nghiệp",
                  );
                },
                onTap: () {
                  AppBottomSheet.showNormalBottomSheet(
                    context,
                    title: "Nghề nghiệp",
                    height: MediaQuery.of(context).size.height * 0.8,
                    child: SelectProfession(
                      arguments: SelectProfessionArguments(
                        profession: state.profession,
                        listProfession: state.listProfession,
                        onConfirm: (value) {
                          ref
                              .read(setupAccountProvider.notifier)
                              .selectProfession(value);
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
          if (state.entityGroup == CategoryRegisterEnum.hkd ||
              state.entityType == CategoryRegisterEnum.personal) ...[
            Padding(
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing4,
              ),
              child: TextFieldWidget(
                key: const ValueKey('numberCCCD'),
                labelText: 'CCCD/Số đăng ký kinh doanh',
                isRequired: true,
                maxLength: 12,
                controller: numberCCCDController,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.done,
                validator: (value) {
                  return ValidateUtils.onValidateNotNull(
                    value: value,
                    title: 'CCCD/Số đăng ký kinh doanh',
                  );
                },
              ),
            ),
          ],
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              key: const ValueKey('taxCode'),
              labelText: 'Mã số thuế',
              isRequired: true,
              controller: taxCodeController,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.done,
              validator: (value) {
                return ValidateUtils.onValidateTax(value);
              },
            ),
          ),
          BuildImageInformation(
            arguments: BuildImageInformationArguments(
              title: 'Ảnh ĐKKD/MST',
              images: state.listImageCode,
              uploadStatus: state.uploadImageCodeStatus,
              deleteImage: (image) {
                ref.read(setupAccountProvider.notifier).deleteImageCode(image);
              },
              addImage: (images) {
                ref.read(setupAccountProvider.notifier).addImagesCode(
                      images: images,
                    );
              },
            ),
          ),
          if (state.entityGroup == CategoryRegisterEnum.hkd) ...[
            BuildImageInformation(
              arguments: BuildImageInformationArguments(
                title: 'Ảnh hộ khẩu',
                images: state.listImageHousehold,
                uploadStatus: state.uploadImageHouseholdStatus,
                deleteImage: (image) {
                  ref
                      .read(setupAccountProvider.notifier)
                      .deleteImageHousehold(image);
                },
                addImage: (images) {
                  ref.read(setupAccountProvider.notifier).addImagesHousehold(
                        images: images,
                      );
                },
              ),
            ),
          ],
          if (!(state.entityGroup == CategoryRegisterEnum.hkd &&
              state.entityType == CategoryRegisterEnum.personal)) ...[
            buildBankInformation(),
          ]
        ],
      ),
    );
  }

  Widget buildBankInformation() {
    var state = ref.watch(setupAccountProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(
            top: BaseSpacing.spacing1,
            bottom: BaseSpacing.spacing3,
          ),
          child: Text(
            'Thông tin ngân hàng',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textBody,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
            bottom: BaseSpacing.spacing4,
          ),
          child: TextFieldWidget(
            key: const ValueKey('bankAccount'),
            labelText: 'Số tài khoản',
            isRequired: true,
            controller: bankAccountNumberController,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.done,
            validator: (value) {
              return ValidateUtils.onValidateNotNull(
                value: value,
                title: 'Số tài khoản',
              );
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
            bottom: BaseSpacing.spacing4,
          ),
          child: DropDownComplainWidget(
            data: state.listBank ?? [],
            title: "Ngân hàng",
            isRequired: true,
            onChangeItem: (value) {
              ref.read(setupAccountProvider.notifier).selectBank(value);
            },
            validator: (value) {
              return ValidateUtils.onValidateNotNull(
                value: value,
                title: 'Ngân hàng',
              );
            },
            selected: state.bankSelected,
            displayValue: (item) => item.name ?? '',
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
            bottom: BaseSpacing.spacing4,
          ),
          child: TextFieldWidget(
            key: const ValueKey('bankBranch'),
            labelText: 'Chi nhánh',
            isRequired: true,
            controller: bankBranchController,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.done,
            validator: (value) {
              return ValidateUtils.onValidateNotNull(
                value: value,
                title: 'Chi nhánh',
              );
            },
          ),
        ),
      ],
    );
  }

  void onSelectAddress({
    AddressInfo? addressInfo,
  }) async {
    AddressEntity? address;
    if (addressInfo != null) {
      address = AddressEntity(
        addressDetail: addressInfo.addressDetail,
        ward: addressInfo.ward,
        district: addressInfo.district,
        province: addressInfo.province,
        isGetFromLocal: addressInfo.isGetFromLocal,
      );
    }

    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        addressSelected: address,
        onSelectAddress: (address) {
          ref.read(setupAccountProvider.notifier).selectAddress(
                address.convertToAddressInfo,
              );
        },
      ),
    );
  }

  void openDatetimePicker({
    required DateTime date,
    DateTime? minDate,
    DateTime? maxDate,
    required Function(DateTime date) onSubmit,
  }) {
    DateTime? minDateTime;
    DateTime? maxDateTime;
    DateTime initialDateTime =
        DateTime(date.year, date.month, date.day, 0, 0, 0);
    if (minDate != null) {
      minDateTime = DateTime(minDate.year, minDate.month, minDate.day, 0, 0, 0);
      if (initialDateTime.isBefore(minDateTime)) {
        initialDateTime = minDateTime;
      }
    }
    if (maxDate != null) {
      maxDateTime =
          DateTime(maxDate.year, maxDate.month, maxDate.day, 23, 59, 59);
      if (initialDateTime.isAfter(maxDateTime)) {
        initialDateTime = maxDateTime;
      }
    }

    CustomBottomPicker.date(
      height: MediaQuery.of(context).size.height * 0.40,
      title: "Chọn thời gian",
      buttonText: "Xác nhận",
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      initialDateTime: initialDateTime,
      titleStyle: UITextStyle.body1SemiBold,
      dateOrder: DatePickerDateOrder.dmy,
      pickerTextStyle: TextStyle(
        fontSize: 18,
        color: BaseColors.backgroundBlack,
      ),
      onSubmit: (date) => onSubmit(date),
    ).show(context);
  }

  Widget buildRepresentativeInformation() {
    var state = ref.watch(setupAccountProvider);
    return Column(
      children: [
        const DividerWidget(
          height: BaseSpacing.spacing2,
        ),
        Padding(
          padding: const EdgeInsets.only(
            top: BaseSpacing.spacing4,
            left: BaseSpacing.spacing4,
            right: BaseSpacing.spacing4,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing4,
                ),
                child: Text(
                  'Thông tin người đại diện',
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing4,
                ),
                child: TextFieldWidget(
                  key: const ValueKey('fullNameRepresentative'),
                  labelText: 'Họ và tên',
                  isRequired: true,
                  controller: fullNameRepresentativeController,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.done,
                  onChanged: (value) {
                    String formatted = StringUtils.formatRegister(value);
                    if (value != formatted) {
                      fullNameRepresentativeController.value =
                          fullNameRepresentativeController.value.copyWith(
                        text: formatted,
                        selection: TextSelection.collapsed(
                          offset: formatted.length,
                        ),
                      );
                    }
                  },
                  validator: (value) {
                    return ValidateUtils.onValidateRegister(
                      value: value,
                      title: 'Họ và tên',
                    );
                  },
                ),
              ),
              if (state.entityType == CategoryRegisterEnum.personal) ...[
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: BaseSpacing.spacing4,
                  ),
                  child: TextFieldWidget(
                    key: const ValueKey('position'),
                    labelText: "Chức vụ",
                    enabled: false,
                    controller: TextEditingController(
                      text: 'Chủ hộ',
                    ),
                  ),
                ),
              ],
              if ((state.entityGroup == CategoryRegisterEnum.agency ||
                      state.entityGroup == CategoryRegisterEnum.oft) &&
                  state.entityType == CategoryRegisterEnum.company) ...[
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: BaseSpacing.spacing4,
                  ),
                  child: TextFieldWidget(
                    key: const ValueKey('positionRepresentative'),
                    labelText: 'Chức vụ',
                    isRequired: true,
                    controller: positionRepresentativeController,
                    keyboardType: TextInputType.text,
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidateRegister(
                        value: value,
                        title: 'Chức vụ',
                      );
                    },
                    onChanged: (value) {
                      String formatted = StringUtils.formatRegister(value);
                      if (value != formatted) {
                        positionRepresentativeController.value =
                            positionRepresentativeController.value.copyWith(
                          text: formatted,
                          selection: TextSelection.collapsed(
                            offset: formatted.length,
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],
              if (state.entityGroup == CategoryRegisterEnum.hkd &&
                  state.entityType == CategoryRegisterEnum.company) ...[
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: BaseSpacing.spacing4,
                  ),
                  child: TextFieldWidget(
                    key: const ValueKey('numberCCCDRepresentative'),
                    labelText: 'CCCD',
                    maxLength: 12,
                    isRequired: true,
                    controller: numberCCCDRepresentativeController,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidateCCCD(value);
                    },
                    onChanged: (value) {
                      String formatted = StringUtils.formatInputNumber(value);
                      if (value != formatted) {
                        numberCCCDRepresentativeController.value =
                            numberCCCDRepresentativeController.value.copyWith(
                          text: formatted,
                          selection: TextSelection.collapsed(
                            offset: formatted.length,
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],
              if (state.entityType == CategoryRegisterEnum.company) ...[
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: BaseSpacing.spacing4,
                  ),
                  child: TextFieldWidget(
                    key: const ValueKey('phoneRepresentative'),
                    labelText: 'Số điện thoại',
                    isRequired: true,
                    maxLength: 10,
                    controller: phoneRepresentativeController,
                    keyboardType: TextInputType.phone,
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidatePhone(value);
                    },
                  ),
                ),
              ],
              if (state.entityType == CategoryRegisterEnum.personal) ...[
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: BaseSpacing.spacing4,
                  ),
                  child: TextFieldWidget(
                    key: const ValueKey('nationalityRepresentative'),
                    labelText: 'Quốc tịch',
                    isRequired: true,
                    controller: nationalityRepresentativeController,
                    keyboardType: TextInputType.text,
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidateRegister(
                        value: value,
                        title: 'Quốc tịch',
                      );
                    },
                    onChanged: (value) {
                      String formatted = StringUtils.formatRegister(value);
                      if (value != formatted) {
                        nationalityRepresentativeController.value =
                            nationalityRepresentativeController.value.copyWith(
                          text: formatted,
                          selection: TextSelection.collapsed(
                            offset: formatted.length,
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget buildAccountInformation() {
    var state = ref.watch(setupAccountProvider);
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: Text(
              'Thông tin tài khoản',
              style: UITextStyle.body1SemiBold.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              key: const ValueKey('password'),
              labelText: "Mật khẩu",
              isRequired: true,
              controller: passwordController,
              keyboardType: TextInputType.text,
              textInputAction: TextInputAction.done,
              obscureText: state.isPasswordObscureText,
              suffix: InkWell(
                onTap: () {
                  ref.read(setupAccountProvider.notifier).showPassword(
                        isPasswordObscureText: !state.isPasswordObscureText,
                      );
                },
                child: state.isPasswordObscureText
                    ? MyAssets.icons.eye.svg()
                    : MyAssets.icons.eyeSlash.svg(),
              ),
              validator: (value) {
                return ValidateUtils.onValidatePasswordRegister(value);
              },
            ),
          ),
          TextFieldWidget(
            key: const ValueKey('confirmPassword'),
            labelText: "Xác nhận mật khẩu",
            isRequired: true,
            keyboardType: TextInputType.text,
            controller: confirmPasswordController,
            textInputAction: TextInputAction.done,
            obscureText: state.isConfirmPasswordObscureText,
            suffix: InkWell(
              onTap: () {
                ref.read(setupAccountProvider.notifier).showPassword(
                      isConfirmPasswordObscureText:
                          !state.isConfirmPasswordObscureText,
                    );
              },
              child: state.isConfirmPasswordObscureText
                  ? MyAssets.icons.eye.svg()
                  : MyAssets.icons.eyeSlash.svg(),
            ),
            validator: (value) {
              if (value != passwordController.text) {
                return "Mật khẩu không trùng khớp.";
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget buildOtherInformation() {
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: Text(
              'Thông tin khác',
              style: UITextStyle.body1SemiBold.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing3,
            ),
            child: titleRequired(
              title: 'Người giới thiệu',
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              key: const ValueKey('fullNameReferrer'),
              labelText: 'Họ và tên',
              isRequired: true,
              controller: fullNameReferrerController,
              keyboardType: TextInputType.text,
              textInputAction: TextInputAction.done,
              validator: (value) {
                return ValidateUtils.onValidateRegister(
                  value: value,
                  title: 'Họ và tên',
                );
              },
              onChanged: (value) {
                String formatted = StringUtils.formatRegister(value);
                if (value != formatted) {
                  fullNameReferrerController.value =
                      fullNameReferrerController.value.copyWith(
                    text: formatted,
                    selection: TextSelection.collapsed(
                      offset: formatted.length,
                    ),
                  );
                }
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              key: const ValueKey('phoneReferrer'),
              labelText: 'Số điện thoại',
              isRequired: true,
              maxLength: 10,
              controller: phoneReferrerController,
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.done,
              validator: (value) {
                return ValidateUtils.onValidatePhone(value);
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing6,
            ),
            child: TextFieldWidget(
              key: const ValueKey('referralCode'),
              labelText: 'Mã giới thiệu',
              controller: referralCodeController,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.done,
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  RegExp(r'[0-9]'),
                ),
              ],
            ),
          ),
          buildInformationRecipient(),
        ],
      ),
    );
  }

  Widget buildInformationRecipient() {
    var state = ref.watch(setupAccountProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(
            bottom: BaseSpacing.spacing3,
          ),
          child: InkWellWidget(
            onTap: () {
              AppDialog.showDialogInfo(
                context,
                title: 'Người nhận thông tin',
                message:
                    'Đây là số điện thoại sẽ nhận thông báo khi CTV được phê duyệt/từ chối qua Mocha',
              );
            },
            child: Row(
              children: [
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      right: BaseSpacing.spacing1,
                    ),
                    child: titleRequired(
                      title: 'Người nhận thông tin',
                    ),
                  ),
                ),
                MyAssets.icons.iconInfoCircleS16.svg(),
              ],
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: BaseColors.borderDefault,
            ),
            borderRadius: BorderRadius.circular(
              BaseSpacing.spacing2,
            ),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: BaseSpacing.spacing3,
          ),
          child: Column(
            children: [
              if (state.loadStatusListPhone == LoadStatus.loading) ...[
                const Center(child: LoadingIndicatorWidget()),
              ],
              if (state.loadStatusListPhone == LoadStatus.initial ||
                  state.loadStatusListPhone == LoadStatus.success) ...[
                ListView.builder(
                  shrinkWrap: true,
                  itemCount: (state.listPhone ?? []).length,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Row(
                          children: [
                            if ((state.listPhone ?? []).length > 1) ...[
                              InkWellWidget(
                                onTap: () {
                                  ref
                                      .read(setupAccountProvider.notifier)
                                      .removePhone(index);
                                },
                                child: MyAssets.icons.icMinus.svg(),
                              ),
                            ],
                            Expanded(
                              child: TextFieldWidget(
                                clear: false,
                                maxLength: 10,
                                labelText: "Nhập số điện thoại",
                                keyboardType: TextInputType.phone,
                                textInputAction: TextInputAction.done,
                                border: Border.all(
                                  color: Colors.transparent,
                                ),
                                onChanged: (value) {
                                  state.listPhone![index] = value;
                                },
                                controller: TextEditingController(
                                  text: state.listPhone![index],
                                ),
                                validator: (value) {
                                  return ValidateUtils.onValidatePhone(value);
                                },
                              ),
                            ),
                          ],
                        ),
                        DividerWidget(
                          height: BaseSpacing.spacingPx,
                          color: BaseColors.secondaryBackground,
                        ),
                      ],
                    );
                  },
                ),
              ],
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: BaseSpacing.spacing1,
                ),
                child: BaseButton(
                  text: 'Thêm người nhận',
                  icon: MyAssets.icons.icAdd.svg(),
                  backgroundColor: BaseColors.backgroundWhite,
                  textColor: BaseColors.primary,
                  onTap: () {
                    ref.read(setupAccountProvider.notifier).addNewPhone();
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget titleRequired({
    String? title,
  }) {
    return RichText(
      text: TextSpan(
        style: UITextStyle.body2Medium,
        children: <TextSpan>[
          TextSpan(
            text: '$title',
          ),
          TextSpan(
            text: ' *',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> onSignContract(BuildContext context) async {
    var state = ref.watch(setupAccountProvider);
    FocusScope.of(context).unfocus();
    if (formProcessKey.currentState!.validate()) {
      if (state.listImageCode == null) {
        return ErrorDialog.showErrorDialog(
          'Vui lòng chụp ảnh ĐKKD/MST',
        );
      }

      if (state.entityGroup == CategoryRegisterEnum.hkd &&
          state.listImageHousehold == null) {
        return ErrorDialog.showErrorDialog(
          'Vui lòng chụp ảnh hộ khẩu',
        );
      }

      if (state.listPhone != null &&
          state.listPhone?.toSet().length != state.listPhone?.length) {
        return ErrorDialog.showErrorDialog(
          'Vui lòng không nhập số điện thoại Người nhận thông tin trùng nhau',
        );
      }

      final RegisterBody body = RegisterBody(
        listImageCMT: state.listImageCode
                ?.map(
                  (item) => RegisterImage(
                    name: item.name,
                    base64String: item.base64,
                  ),
                )
                .toList() ??
            [],
        listImageHK: state.listImageHousehold
                ?.map(
                  (item) => RegisterImage(
                    name: item.name,
                    base64String: item.base64,
                  ),
                )
                .toList() ??
            [],
        sysUserDTO: SysUserDto(
          accountNumber: phoneVTController.text != ''
              ? phoneVTController.text
              : bankAccountNumberController.text,
          address: state.address?.getFullAddress,
          contractCode: state.contractNumber,
          email: '',
          fieldType: '${state.listBusinessHeadquarterSelected?.map(
                (item) => item.value,
              ).join(',')}',
          fullName: nameController.text,
          occupation: '${state.listGoodsSelected?.map(
                (item) => item.name,
              ).join(' - ')}',
          parentPhone: phoneReferrerController.text,
          parentName: fullNameReferrerController.text,
          referralCode: referralCodeController.text,
          password: StringUtils.encodeCharacter(passwordController.text),
          phoneNumber: phoneController.text,
          sysGroupId: '${state.managementUnit?.district?.sysGroupId}',
          taxCode: numberCCCDController.text,
          taxCodeUser: taxCodeController.text,
          typeCheckUser: 1,
          typeUser: state.entityGroup == CategoryRegisterEnum.hkd &&
                  state.entityType == CategoryRegisterEnum.company
              ? 4
              : state.entityGroup?.value,
          userBirthday: state.entityGroup != CategoryRegisterEnum.agency &&
                  state.entityType == CategoryRegisterEnum.personal
              ? '${state.dateOfBirth?.displayView(
                  format: DateTimeFormater.dateFormatVi,
                )}'
              : state.entityGroup == CategoryRegisterEnum.hkd &&
                      state.entityType == CategoryRegisterEnum.company
                  ? '${state.dateOfEstablishment?.displayView(
                      format: DateTimeFormater.dateFormatVi,
                    )}'
                  : null,
          byteCtvSign: null,
          sysUserId: '',
          otp: '',
          filePathContractCTV: '',
          typeAgency: '',
          sourceAgency: state.agentSourceSelected?.name,
          representDl: fullNameRepresentativeController.text,
          positionDl: positionRepresentativeController.text != ''
              ? positionRepresentativeController.text
              : "Chủ hộ",
          provinceIdCtvXddd: state.address?.province?.id,
          provinceNameCtvXddd: state.address?.province?.name,
          districtName: state.address?.district?.name,
          communeName: state.address?.ward?.name,
          effectiveDate: '${state.effectiveDate?.displayView(
            format: DateTimeFormater.dateFormatVi,
          )}',
          bank: state.bankSelected?.name,
          bankBranch: bankBranchController.text,
          contactName: '',
          parentUserId: '',
          lat: '',
          lng: '',
          aioConfigLevelCodeDTOS: state.industryGoods?.entries
              .map(
                (entry) => AioConfigLevelCodeDto(
                  field: entry.key,
                  fieldGPTH: false,
                  fieldTTHT: false,
                  fieldDVKT: false,
                  listIndustryCode: entry.value,
                  fieldType:
                      SetupAccountIndustryTypeExtension.fromKey(entry.key),
                  levelCode: '${state.listGoodsSelected?.map(
                        (item) => item.levelCode,
                      ).join(',')}',
                ),
              )
              .toList(),
          nameRepresent: fullNameRepresentativeController.text,
          receiverPhoneNumber: '${state.listPhone?.map(
                (item) => item,
              ).join(',')}',
          sysUserIdUpdate:
              widget.arguments.isDetail ? widget.arguments.sysUserId : null,
          portraitCode: state.entityGroup == CategoryRegisterEnum.hkd
              ? state.profession?.code
              : state.entityGroup == CategoryRegisterEnum.agency
                  ? state.agentPortraitSelected?.code
                  : '',
          portraitDetail: '',
          phoneRepresent: phoneRepresentativeController.text,
          citizenIdRepresent: numberCCCDRepresentativeController.text,
          agencyAccountNumber: bankAccountNumberController.text,
          representPhoneNumber: phoneRepresentativeController.text,
          nationality: nationalityRepresentativeController.text,
          subTypeUser: state.entityGroup == CategoryRegisterEnum.hkd
              ? 1
              : state.entityType?.value,
          dkkd: numberCCCDController.text,
          bankNumber: bankAccountNumberController.text,
          employeeCode: '',
        ),
      );

      if (await ref
          .read(setupAccountProvider.notifier)
          .onUpdateImage(body: body)) {
        if (!context.mounted) return;
        context.push(
          RouterPaths.signContractRegister,
          extra: SignContractRegisterArguments(
            content: state.entityGroup == CategoryRegisterEnum.hkd
                ? 'Chữ ký HKD'
                : state.entityGroup == CategoryRegisterEnum.agency
                    ? 'Chữ ký ĐLBH'
                    : 'Chữ ký OFT',
            buttonTitle: 'Ký hợp đồng',
            orderCode: '',
            registerBody: body,
            sendOtpType: SendOtpType.register,
            phoneNumber: phoneController.text.trim(),
            onCompleted: () {
              if (!context.mounted) return;
              AppDialog.showDialogCenter(
                context,
                message: "Đăng ký thành công",
                status: DialogStatus.success,
                onConfirm: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                  if (widget.arguments.isDetail) {
                    widget.arguments.onCompletedDetail?.call();
                  } else {
                    context.pushReplacement(
                      RouterPaths.login,
                    );
                  }
                },
              );
            },
          ),
        );
      } else {
        return ErrorDialog.showErrorDialog(
          'Tải ảnh không thành công',
        );
      }
    }
  }
}
