import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/generated/l10n.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/authentication/setup_account/setup_account_page.dart';
import 'package:vcc/presentation/views/features/authentication/verify_otp/verify_otp_page.dart';
import 'package:vcc/presentation/views/widgets/pages/web_view_screen.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/validate_utils.dart';

class RegisterPage extends StatefulHookConsumerWidget {
  const RegisterPage({super.key});

  @override
  ConsumerState<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends ConsumerState<RegisterPage> {
  late TextEditingController phoneNumberController;

  @override
  void initState() {
    phoneNumberController = TextEditingController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      appBar: AppBarCustom(
        title: AppStrings.of(context).register,
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 24,
          ),
          child: RichText(
            text: TextSpan(
              style: UITextStyle.body2Regular.copyWith(
                color: BaseColors.textBody,
              ),
              children: [
                const TextSpan(
                  text:
                      "Bằng cách đăng nhập/đăng ký, bạn đã hiểu và đồng ý với ",
                ),
                TextSpan(
                  text: "Điều khoản sử dụng",
                  style: UITextStyle.body2Regular.copyWith(
                    color: BaseColors.info,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      context.push(
                        RouterPaths.webView,
                        extra: WebViewArguments(
                          url: BaseConstant.termUrl,
                          title: "Điều khoản sử dụng",
                        ),
                      );
                    },
                ),
                const TextSpan(
                  text: ' và ',
                ),
                TextSpan(
                  text: "Chính sách bảo mật",
                  style: UITextStyle.body2Regular.copyWith(
                    color: BaseColors.info,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      context.push(
                        RouterPaths.webView,
                        extra: WebViewArguments(
                          url: BaseConstant.termUrl,
                          title: "Chính sách bảo mật",
                        ),
                      );
                    },
                ),
                const TextSpan(
                  text: " của Viettel Construction.",
                ),
              ],
            ),
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 24,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              BaseText(
                content: "Nhập thông tin để bắt đầu đăng ký",
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
              const SizedBox(height: 24),
              TextFieldWidget(
                controller: phoneNumberController,
                labelText: "Số điện thoại",
                isRequired: true,
                textInputAction: TextInputAction.done,
                maxLength: 11,
                keyboardType: TextInputType.phone,
                prefix: MyAssets.icons.iconMobileS24.svg(),
                validator: (value) {
                  return ValidateUtils.onValidatePhone(value);
                },
              ),
              const SizedBox(height: 40),
              Semantics(
                identifier: "btnSendOtpRegisterAccount",
                child: BaseButton(
                  text: "Gửi mã xác thực",
                  onTap: () {
                    context.push(
                      RouterPaths.verifyOtp,
                      extra: VerifyOtpArguments(
                        phoneNumber: phoneNumberController.text,
                        onVerifySuccess: () {
                          context.push(
                            RouterPaths.setupAccount,
                            extra: SetupAccountArguments(),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Bạn đã có tài khoản?',
                    style: UITextStyle.body2Regular.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Semantics(
                    identifier: "btnLoginNow",
                    child: AppTextButton(
                      title: 'Đăng nhập ngay',
                      padding: EdgeInsets.zero,
                      onTap: () {
                        context.pop();
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
