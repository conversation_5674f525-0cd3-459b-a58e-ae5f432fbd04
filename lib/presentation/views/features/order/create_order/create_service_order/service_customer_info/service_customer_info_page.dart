import 'dart:async';

import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/entities/address_info.dart';
import 'package:vcc/domain/entities/association/association_entity.dart';
import 'package:vcc/domain/entities/collection_info_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/entities/order/user_rate_entity.dart';
import 'package:vcc/domain/entities/service/date_status_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/enums/gender_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/params/product_order_param.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_service_type/select_service_type_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/verify_service_order/verify_service_order_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/year_service_info/year_service_info_view.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/create_service_order_view_model.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/select_date_service/select_date_service_view.dart';
import 'package:vcc/presentation/views/features/store/service/list_package_service/list_package_service_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/cus360_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/input_formatter_utils.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

class ServiceCustomerInfoPage extends StatefulHookConsumerWidget {
  final CollectionInfoEntity? collectionInfo;
  final AssociationEntity? associationInfo;
  final Function? onNextPageCallBack;

  const ServiceCustomerInfoPage({
    super.key,
    this.collectionInfo,
    this.associationInfo,
    this.onNextPageCallBack,
  });

  @override
  ConsumerState<ServiceCustomerInfoPage> createState() =>
      _ServiceCustomerInfoPageState();
}

class _ServiceCustomerInfoPageState
    extends ConsumerState<ServiceCustomerInfoPage>
    with AutomaticKeepAliveClientMixin {
  late TextEditingController userPhoneNumber;
  late TextEditingController userName;

  late TextEditingController userPhoneNumberSetup;
  late TextEditingController userNameSetup;

  late TextEditingController taxCompany;
  late TextEditingController companyPhoneNumber;
  late TextEditingController companyName;
  late TextEditingController companyUserName;
  late TextEditingController companyUserPhoneNumber;

  late TextEditingController companyUserNameSetup;
  late TextEditingController companyUserPhoneNumberSetup;

  late FocusNode userPhoneFocusNode;
  late FocusNode taxFocusNode;

  late TextEditingController noteController;

  final formUserKey = GlobalKey<FormState>();
  final formCompanyKey = GlobalKey<FormState>();

  final formUserSetupKey = GlobalKey<FormState>();
  final formCompanySetupKey = GlobalKey<FormState>();

  late StreamSubscription _showAlertSubscription;

  @override
  void initState() {
    userPhoneNumber = TextEditingController();
    userName = TextEditingController();

    userPhoneNumberSetup = TextEditingController();
    userNameSetup = TextEditingController();

    taxCompany = TextEditingController();
    companyName = TextEditingController();
    companyPhoneNumber = TextEditingController();
    companyUserName = TextEditingController();
    companyUserPhoneNumber = TextEditingController();

    companyUserNameSetup = TextEditingController();
    companyUserPhoneNumberSetup = TextEditingController();

    noteController = TextEditingController();

    userPhoneFocusNode = FocusNode();
    taxFocusNode = FocusNode();

    _showAlertSubscription = ref
        .read(createServiceOrderProvider.notifier)
        .alertController
        .stream
        .listen((data) {
      var state = ref.watch(createServiceOrderProvider);

      if (state.unCheckAddress) {
        ref.read(createServiceOrderProvider.notifier).selectAddress(
              data[0],
              isSetup: data[2],
            );
      } else {
        validateAddress(
          address: data[0],
          totalPrice: data[1],
          isSetup: data[2],
        );
      }
    });
    Future(
      () {
        if (widget.collectionInfo != null) {
          ref
              .read(createServiceOrderProvider.notifier)
              .fillDataFromCollectionInfo(widget.collectionInfo!);

          var state = ref.watch(createServiceOrderProvider);
          var item = widget.collectionInfo!;

          if (state.userType == UserType.personal) {
            userName.text = item.customerName ?? "";
            userPhoneNumber.text = item.customerPhone ?? "";

            userNameSetup.text = item.customerName ?? "";
            userPhoneNumberSetup.text = item.customerPhone ?? "";
          } else {
            taxCompany.text = item.companyTaxCode ?? "";
            companyName.text = item.companyName ?? "";
            companyPhoneNumber.text = item.companyPhoneNumber ?? "";

            companyUserName.text = item.customerName ?? "";
            companyUserPhoneNumber.text = item.customerPhone ?? "";
            companyUserNameSetup.text = item.customerName ?? "";
            companyUserPhoneNumberSetup.text = item.customerPhone ?? "";
          }
        }

        if (widget.associationInfo != null) {
          ref.read(createServiceOrderProvider.notifier).fillDataFromAssociation(
                widget.associationInfo!,
              );

          var state = ref.watch(createServiceOrderProvider);
          var item = widget.associationInfo!;

          if (state.userType == UserType.personal) {
            userName.text = item.customerName ?? "";
            userPhoneNumber.text = item.customerPhone ?? "";

            userNameSetup.text = item.customerName ?? "";
            userPhoneNumberSetup.text = item.customerPhone ?? "";
          } else {
            taxCompany.text = item.companyTaxCode ?? "";
            companyName.text = item.companyName ?? "";
            companyPhoneNumber.text = item.companyPhone ?? "";

            companyUserName.text = item.customerName ?? "";
            companyUserPhoneNumber.text = item.customerPhone ?? "";
            companyUserNameSetup.text = item.customerName ?? "";
            companyUserPhoneNumberSetup.text = item.customerPhone ?? "";
          }
        }

        if (ref.watch(createServiceOrderProvider).speedOrderType ==
            ServiceType.salePoint) {
          ref.read(createServiceOrderProvider.notifier).fillDataFromUserInfo();

          var item = GlobalData.instance;
          userName.text = item.userInfo?.fullName ?? "";
          userPhoneNumber.text = item.userInfo?.phoneNumber ?? "";
          userNameSetup.text = item.userInfo?.fullName ?? "";
          userPhoneNumberSetup.text = item.userInfo?.phoneNumber ?? "";
          checkStaff(
            context: context,
            phoneNumber: item.userInfo?.phoneNumber,
          );
        }
      },
    );

    super.initState();
  }

  void showAddressDifferentDefault({
    AddressInfo? address,
    int? price,
    required bool isSetup,
  }) {
    AppDialog.showDialogConfirm(
      context,
      title: "Đồng ý",
      message:
          "Địa chỉ triển khai khác với địa chỉ tham khảo, giá dịch vụ có thể thay đổi. Bạn xác nhận thay đổi địa chỉ triển khai?",
      onConfirmAction: () async {
        ref.read(createServiceOrderProvider.notifier).selectAddress(
              address!,
              isSetup: isSetup,
            );
        ref
            .read(createServiceOrderProvider.notifier)
            .updateListService(address: address);
        if (price != null) {
          ref
              .read(createServiceOrderProvider.notifier)
              .changeTotalAmount(price);
        }
      },
    );
  }

  void showDialogChangeAddress({
    AddressInfo? address,
    int? price,
    required bool isSetup,
  }) {
    AppDialog.showDialogConfirm(
      context,
      title: "Đồng ý",
      message:
          "Thay đổi địa chỉ sẽ thay đổi giá của dịch vụ, vật tư, khuyến mãi và thời gian đáp ứng của thợ. Bạn xác nhận muốn thay đổi địa chỉ triển khai?",
      onConfirmAction: () async {
        ref.read(createServiceOrderProvider.notifier).selectAddress(
              address!,
              isSetup: isSetup,
            );
        ref
            .read(createServiceOrderProvider.notifier)
            .updateListService(address: address);

        if (price != null) {
          ref
              .read(createServiceOrderProvider.notifier)
              .changeTotalAmount(price);
        }
      },
    );
  }

  void showDialogConfirmTime(
    DateStatusEntity date, {
    int? price,
  }) {
    String message =
        "Thay đổi thời gian triển khai có thể thay đổi giá, khuyến mại. Bạn xác nhận thay đổi thời gian triển khai?";

    AppDialog.showDialogConfirm(
      context,
      title: "Đồng ý",
      message: message,
      onConfirmAction: () async {
        ref.read(createServiceOrderProvider.notifier).setUpTime(date);
        ref
            .read(createServiceOrderProvider.notifier)
            .updateListService(startTime: date.getDate);

        if (price != null) {
          ref
              .read(createServiceOrderProvider.notifier)
              .changeTotalAmount(price);
        }
      },
    );
  }

  void showDialogConfirmSpeedTime({
    required bool isSetup,
  }) {
    if (isSetup) {
      bool isWeekend =
          DateTime.now().weekday == 6 || DateTime.now().weekday == 7;
      String message = isWeekend
          ? "Bạn vừa chọn hẹn lịch trong 3 giờ vào cuối tuần, đơn hàng sẽ được tính thêm phụ phí 100.000đ và giá dich vụ sẽ thay đổi. Bạn vui lòng xác nhận?"
          : "Bạn vừa chọn hẹn lịch trong 3 giờ, đơn hàng sẽ được tính thêm phụ phí 100.000đ. Bạn vui lòng xác nhận?";

      AppDialog.showDialogConfirm(
        context,
        title: "Xác nhận",
        message: message,
        onConfirmAction: () async {
          ref
              .read(createServiceOrderProvider.notifier)
              .changeSetupSpeedService(true);
        },
      );
    } else {
      ref
          .read(createServiceOrderProvider.notifier)
          .changeSetupSpeedService(false);
    }
  }

  @override
  void dispose() {
    userPhoneNumber.dispose();
    userName.dispose();
    userPhoneNumberSetup.dispose();
    userNameSetup.dispose();
    taxCompany.dispose();
    companyName.dispose();
    companyPhoneNumber.dispose();
    companyUserName.dispose();
    companyUserPhoneNumber.dispose();

    companyUserNameSetup.dispose();
    companyUserPhoneNumberSetup.dispose();

    noteController.dispose();

    userPhoneFocusNode.dispose();
    taxFocusNode.dispose();

    _showAlertSubscription.cancel();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var state = ref.watch(createServiceOrderProvider);
    return Stack(
      children: [
        SingleChildScrollView(
          padding: const EdgeInsets.only(bottom: 90),
          child: InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: Container(
              color: BaseColors.backgroundWhite,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  _buildCustomerInfo(),
                  DividerWidget(
                    height: 8,
                    color: BaseColors.backgroundGray,
                  ),
                  _buildSetupInfo(),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            color: BaseColors.backgroundWhite,
            child: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Semantics(
                      identifier: "btnNextStepTwo",
                      child: BaseButton(
                        text: "Tiếp tục",
                        onTap: onSaveInfo,
                      ),
                    ),
                    const InkWellWidget(),
                  ],
                ),
              ),
            ),
          ),
        ),
        if (state.checkStaffStatus == LoadStatus.loading ||
            state.loadCustomerStatus == LoadStatus.loading) ...[
          const Center(
            child: LoadingIndicatorWidget(),
          )
        ],
      ],
    );
  }

  Widget _buildCustomerInfo() {
    var state = ref.watch(createServiceOrderProvider);

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            "Thông tin khách hàng",
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textTitle,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Loại khách hàng ',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 12),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: Row(
              children: <Widget>[
                Expanded(
                  child: Semantics(
                    identifier: "radioPersonalUser",
                    child: RadioWidget<UserType>(
                      value: UserType.personal,
                      groupValue: state.userType,
                      onChanged: (value) {
                        ref
                            .read(createServiceOrderProvider.notifier)
                            .changeUserType(value);
                      },
                      displayWidget: (context, item) {
                        return _displayUserText(item);
                      },
                    ),
                  ),
                ),
                Expanded(
                  child: Semantics(
                    identifier: "radioCompanyUser",
                    child: RadioWidget<UserType>(
                      value: UserType.company,
                      groupValue: state.userType,
                      isDisable: state.speedOrderType == ServiceType.salePoint,
                      onChanged: (value) {
                        ref
                            .read(createServiceOrderProvider.notifier)
                            .changeUserType(value);
                      },
                      displayWidget: (context, item) {
                        return _displayUserText(item);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          state.userType == UserType.personal
              ? _buildUserInfo(context)
              : _buildCompanyInfo(),
        ],
      ),
    );
  }

  Widget _buildSetupInfo() {
    var state = ref.watch(createServiceOrderProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            "Thông tin triển khai",
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textTitle,
            ),
          ),
          state.userType == UserType.personal
              ? _buildUserInfoSetup()
              : _buildCompanyUserInfoSetup(),
        ],
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    var state = ref.watch(createServiceOrderProvider);

    return AbsorbPointer(
      absorbing: state.disableInputForm,
      child: Form(
        key: formUserKey,
        autovalidateMode: state.autoValidateUserForm,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Semantics(
              identifier: "txtUserPhoneNumber",
              child: TextFieldWidget(
                enabled: state.speedOrderType != ServiceType.salePoint,
                controller: userPhoneNumber,
                focusNode: userPhoneFocusNode,
                isRequired: true,
                labelText: "Số điện thoại",
                maxLength: 11,
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.done,
                maxSizeSuffix: true,
                suffix: !state.turnOnEditUserInfo
                    ? Cus360Widget(
                        customerId: state.customerInfo?.customerId,
                        customerPhone: userPhoneNumber.text,
                        customerType: UserType.personal.keyToServer,
                      )
                    : null,
                onChanged: (value) {
                  userPhoneNumberSetup.text = value;
                },
                validator: (value) {
                  final check = ValidateUtils.onValidatePhone(value);

                  return check;
                },
                onFocusChange: (isFocus) async {
                  if (!isFocus) {
                    if (userPhoneNumber.text.validatePhone()) {
                      final result = await ref
                          .read(createServiceOrderProvider.notifier)
                          .checkCommissionConfirm(
                            phoneNumber: userPhoneNumber.text,
                          );

                      if (result != null) {
                        if (result.isShowAlert ?? false) {
                          if (!context.mounted) return;
                          await AppDialog.showDialogConfirm(
                            context,
                            barrierDismissible: false,
                            title: result.title ?? '',
                            widgetContent: _buildContentCommissionConfirm(
                              message: result.message,
                              userRates: result.userRates,
                            ),
                            onConfirmAction: () {
                              checkStaff(
                                context: context,
                                phoneNumber: userPhoneNumber.text,
                              );
                            },
                            onCancelAction: () {
                              userPhoneNumber.text = '';
                              userPhoneNumberSetup.text = '';
                              ref
                                  .read(createServiceOrderProvider.notifier)
                                  .reloadPage();
                            },
                          );
                        } else {
                          if (!context.mounted) return;
                          checkStaff(
                            context: context,
                            phoneNumber: userPhoneNumber.text,
                          );
                        }
                      }
                    } else {
                      ref
                          .read(createServiceOrderProvider.notifier)
                          .enableEditUserForm();
                    }
                  }
                },
              ),
            ),
            if (state.isInternalStaff ?? false) ...[
              const SizedBox(height: 4),
              const Text(
                "Nhân viên nội bộ VCC sẽ hưởng chính sách hoa hồng khác",
                style: BaseStyle.captionLarge,
              ),
            ],
            const SizedBox(height: 16),
            Semantics(
              identifier: "txtUserName",
              child: TextFieldWidget(
                controller: userName,
                isRequired: true,
                labelText: "Họ và tên",
                enabled: state.turnOnEditUserInfo &&
                    state.speedOrderType != ServiceType.salePoint,
                textInputAction: TextInputAction.done,
                textCapitalization: TextCapitalization.words,
                inputFormatters: [
                  SpaceInputFormatter(),
                  FilteringTextInputFormatter.allow(
                    RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
                  ),
                ],
                onChanged: (value) {
                  userNameSetup.text = value;
                },
                onFocusChange: (isFocus) {
                  if (!isFocus) {
                    userName.text = StringUtils.capitalizeEachWord(
                      userName.text,
                    );
                    userNameSetup.text = StringUtils.capitalizeEachWord(
                      userName.text,
                    );
                  }
                },
                validator: (value) {
                  return ValidateUtils.onValidateUserName(value);
                },
              ),
            ),
            const SizedBox(height: 16),
            Semantics(
              identifier: "drlSelectUserAddress",
              child: DropdownWidget(
                labelText: "Địa chỉ",
                isRequired: true,
                content: state.customerAddress?.getFullAddress,
                suffix: MyAssets.icons.iconArrowRightS20.svg(),
                validator: (value) {
                  return ValidateUtils.onValidateAddress(value);
                },
                onTap: () {
                  onSelectAddress(
                    addressInfo: state.customerAddress,
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Giới tính',
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: <Widget>[
                Expanded(
                  child: Semantics(
                    identifier: "radioGenderMale",
                    child: RadioWidget<GenderType?>(
                      value: GenderType.male,
                      groupValue: state.genderType,
                      onChanged: (value) {
                        ref
                            .read(createServiceOrderProvider.notifier)
                            .changeGenderType(value!);
                      },
                      displayWidget: (context, item) {
                        return _displayGenderText(item!);
                      },
                    ),
                  ),
                ),
                Expanded(
                  child: Semantics(
                    identifier: "radioGenderFemale",
                    child: RadioWidget<GenderType?>(
                      value: GenderType.female,
                      groupValue: state.genderType,
                      onChanged: (value) {
                        ref
                            .read(createServiceOrderProvider.notifier)
                            .changeGenderType(value!);
                      },
                      displayWidget: (context, item) {
                        return _displayGenderText(item!);
                      },
                    ),
                  ),
                ),
                Expanded(
                  child: Semantics(
                    identifier: "radioGenderOther",
                    child: RadioWidget<GenderType?>(
                      value: GenderType.different,
                      groupValue: state.genderType,
                      onChanged: (value) {
                        ref
                            .read(createServiceOrderProvider.notifier)
                            .changeGenderType(value!);
                      },
                      displayWidget: (context, item) {
                        return _displayGenderText(item!);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyInfo() {
    var state = ref.watch(createServiceOrderProvider);
    const key = ValueKey("company");

    return Form(
      key: formCompanyKey,
      autovalidateMode: state.autoValidateCompanyForm,
      child: Column(
        key: key,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: Semantics(
              identifier: "txtCompanyTaxCode",
              child: TextFieldWidget(
                controller: taxCompany,
                focusNode: taxFocusNode,
                isRequired: true,
                labelText: "Mã số thuế doanh nghiệp",
                maxLength: 14,
                textInputAction: TextInputAction.done,
                maxSizeSuffix: true,
                suffix: !state.turnOnEditCompanyInfo
                    ? Cus360Widget(
                        taxCode: taxCompany.text,
                        customerType: UserType.company.keyToServer,
                        customerId: state.companyInfo?.customerId,
                      )
                    : null,
                validator: (value) {
                  return ValidateUtils.onValidateTax(value);
                },
                onFocusChange: (isFocus) {
                  if (!isFocus) {
                    getCompanyInfo360(
                      taxCode: taxCompany.text,
                    );
                  }
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: Semantics(
              identifier: "txtCompanyName",
              child: TextFieldWidget(
                controller: companyName,
                labelText: "Tên doanh nghiệp",
                isRequired: true,
                enabled: state.turnOnEditCompanyInfo,
                textInputAction: TextInputAction.done,
                inputFormatters: [
                  SpaceInputFormatter(),
                ],
                validator: (value) {
                  return ValidateUtils.onValidateCompanyName(value);
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
          Semantics(
            identifier: "txtCompanyPhoneNumber",
            child: TextFieldWidget(
              controller: companyPhoneNumber,
              isRequired: true,
              maxLength: 12,
              labelText: "Số điện thoại doanh nghiệp",
              keyboardType: TextInputType.phone,
              enabled: state.turnOnEditCompanyInfo,
              textInputAction: TextInputAction.done,
              validator: (value) {
                return ValidateUtils.onValidateCompanyPhone(value);
              },
            ),
          ),
          const SizedBox(height: 16),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: Semantics(
              identifier: "drlSelectCompanyAddress",
              child: DropdownWidget(
                labelText: "Địa chỉ doanh nghiệp",
                isRequired: true,
                content: state.companyAddress?.getFullAddress,
                suffix: MyAssets.icons.iconArrowRightS20.svg(),
                validator: (value) {
                  return ValidateUtils.onValidateAddress(value);
                },
                onTap: () {
                  onSelectAddress(
                    addressInfo: state.companyAddress,
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: Semantics(
              identifier: "txtCompanyContactPhoneNumber",
              child: TextFieldWidget(
                controller: companyUserPhoneNumber,
                isRequired: true,
                labelText: "Số điện thoại người liên hệ",
                textInputAction: TextInputAction.done,
                keyboardType: TextInputType.phone,
                onChanged: (value) {
                  companyUserPhoneNumberSetup.text = value;
                },
                validator: (value) {
                  return ValidateUtils.onValidatePhone(value);
                },
                onFocusChange: (isFocus) {
                  if (!isFocus) {
                    if (companyUserPhoneNumber.text.validatePhone()) {
                      checkStaffContactCompany(
                        context: context,
                        phoneNumber: companyUserPhoneNumber.text,
                      );
                    }
                  }
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
          Semantics(
            identifier: "txtCompanyContactName",
            child: TextFieldWidget(
              controller: companyUserName,
              isRequired: true,
              labelText: "Họ và tên người liên hệ",
              textInputAction: TextInputAction.done,
              textCapitalization: TextCapitalization.words,
              inputFormatters: [
                SpaceInputFormatter(),
                FilteringTextInputFormatter.allow(
                  RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
                ),
              ],
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  companyUserName.text = StringUtils.capitalizeEachWord(
                    companyUserName.text,
                  );

                  companyUserNameSetup.text = StringUtils.capitalizeEachWord(
                    companyUserName.text,
                  );
                }
              },
              onChanged: (value) {
                companyUserNameSetup.text = value;
              },
              validator: (value) {
                return ValidateUtils.onValidateUserName(value);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoSetup() {
    var state = ref.watch(createServiceOrderProvider);
    //hieptv11: chờ HS triển khai thì mở lại enable nhập
    return Form(
      key: formUserSetupKey,
      autovalidateMode: state.autoValidateUserForm,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const SizedBox(height: 16),
          Semantics(
            identifier: "txtSetupPhoneNumber",
            child: TextFieldWidget(
              controller: userPhoneNumberSetup,
              isRequired: true,
              labelText: "Số điện thoại",
              keyboardType: TextInputType.phone,
              maxLength: 11,
              enabled: false,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              textInputAction: TextInputAction.done,
              validator: (value) {
                return ValidateUtils.onValidatePhone(value);
              },
            ),
          ),
          const SizedBox(height: 16),
          Semantics(
            identifier: "txtSetupUserName",
            child: TextFieldWidget(
              controller: userNameSetup,
              isRequired: true,
              labelText: "Họ và tên",
              enabled: false,
              textInputAction: TextInputAction.done,
              textCapitalization: TextCapitalization.words,
              inputFormatters: [
                SpaceInputFormatter(),
                FilteringTextInputFormatter.allow(
                  RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
                ),
              ],
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  userNameSetup.text = StringUtils.capitalizeEachWord(
                    userNameSetup.text,
                  );
                }
              },
              validator: (value) {
                return ValidateUtils.onValidateUserName(value);
              },
            ),
          ),
          const SizedBox(height: 16),
          Semantics(
            identifier: "drlSelectDeliveryUserAddress",
            child: DropdownWidget(
                labelText: "Địa chỉ triển khai",
                isRequired: true,
                enabled: false,
                content: state.customerSetupAddress?.getFullAddress,
                suffix: MyAssets.icons.iconArrowRightS20.svg(),
                validator: (value) {
                  return ValidateUtils.onValidateAddress(value);
                },
                onTap: () {
                  onSelectAddress(
                    isSetup: true,
                    addressInfo: state.customerSetupAddress,
                  );
                }),
          ),
          if (state.isCreateSpeedOrder ?? false) ...[
            const SizedBox(height: 16),
            _buildSelectService(),
          ],
          if (state.serviceType == ServiceType.package) ...[
            const SizedBox(height: 16),
            _buildYearService(state.serviceYearInfo),
          ],
          const SizedBox(height: 16),
          _buildTimeAndNoteSetup(),
        ],
      ),
    );
  }

  Widget _buildCompanyUserInfoSetup() {
    var state = ref.watch(createServiceOrderProvider);
    const key = ValueKey("company_user_info_setup");

    return Form(
      key: formCompanySetupKey,
      autovalidateMode: state.autoValidateCompanyForm,
      child: Column(
        key: key,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const SizedBox(height: 16),
          Semantics(
            identifier: "txtCompanySetupPhoneNumber",
            child: TextFieldWidget(
              controller: companyUserPhoneNumberSetup,
              isRequired: true,
              labelText: "Số điện thoại",
              enabled: false,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.phone,
              validator: (value) {
                return ValidateUtils.onValidatePhone(value);
              },
            ),
          ),
          const SizedBox(height: 16),
          Semantics(
            identifier: "txtCompanySetupUserName",
            child: TextFieldWidget(
              controller: companyUserNameSetup,
              isRequired: true,
              labelText: "Họ và tên",
              enabled: false,
              textInputAction: TextInputAction.done,
              textCapitalization: TextCapitalization.words,
              inputFormatters: [
                SpaceInputFormatter(),
                FilteringTextInputFormatter.allow(
                  RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
                ),
              ],
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  companyUserNameSetup.text = StringUtils.capitalizeEachWord(
                    companyUserNameSetup.text,
                  );
                }
              },
              validator: (value) {
                return ValidateUtils.onValidateUserName(value);
              },
            ),
          ),
          const SizedBox(height: 16),
          Semantics(
            identifier: "drlSelectDeliveryCompanyAddress",
            child: DropdownWidget(
              labelText: "Địa chỉ triển khai",
              isRequired: true,
              enabled: false,
              content: state.companySetupAddress?.getFullAddress,
              suffix: MyAssets.icons.iconArrowRightS20.svg(),
              validator: (value) {
                return ValidateUtils.onValidateAddress(value);
              },
              onTap: () {
                onSelectAddress(
                  isSetup: true,
                  addressInfo: state.companySetupAddress,
                );
              },
            ),
          ),
          if (state.isCreateSpeedOrder ?? false) ...[
            const SizedBox(height: 16),
            _buildSelectService(),
          ],
          if (state.serviceType == ServiceType.package) ...[
            const SizedBox(height: 16),
            _buildYearService(state.serviceYearInfo),
          ],
          const SizedBox(height: 16),
          _buildTimeAndNoteSetup(),
        ],
      ),
    );
  }

  Widget _displayGenderText(GenderType gender) {
    return Text(
      gender.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _displayUserText(UserType user) {
    return Text(
      user.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _buildTimeAndNoteSetup() {
    var state = ref.watch(createServiceOrderProvider);
    var currentTime = DateTime.now();
    bool showSpeedTime = state.enableSpeedOrder ??
        currentTime.isBefore(
          DateTime(
              currentTime.year, currentTime.month, currentTime.day, 15, 30, 0),
        );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        showSpeedTime
            ? Row(
                children: <Widget>[
                  RichText(
                    text: TextSpan(
                      text: 'Thời gian triển khai',
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.textLabel,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                          text: ' *',
                          style: UITextStyle.body2Medium.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  if (state.serviceType == ServiceType.single ||
                      state.serviceType == ServiceType.package) ...[
                    Semantics(
                      identifier: "iconButtonSpeedService",
                      child: InkWellWidget(
                        onTap: () {
                          showDialogConfirmSpeedTime(
                            isSetup: !state.isSetupSpeedService,
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: state.isSetupSpeedService
                              ? MyAssets.icons.iconChecked.svg()
                              : MyAssets.icons.iconCheckbox.svg(),
                        ),
                      ),
                    ),
                    Text(
                      "Nhanh 3 giờ",
                      style: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ],
                ],
              )
            : Padding(
                padding: const EdgeInsets.only(
                  top: 12,
                  bottom: 11,
                ),
                child: RichText(
                  text: TextSpan(
                    text: 'Thời gian triển khai',
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                    children: <TextSpan>[
                      TextSpan(
                        text: ' *',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
        const SizedBox(height: 8),
        Semantics(
          identifier: "drlSelectTime",
          child: DropdownWidget(
            labelText: "Chọn thời gian",
            enabled: !state.isSetupSpeedService,
            content: state.userType == UserType.personal
                ? state.userTimeSetup?.getTime
                : state.companyTimeSetup?.getTime,
            suffix: MyAssets.icons.iconCalendarS24.svg(),
            validator: (value) {
              if ((value ?? "").isEmpty) {
                return "Vui lòng chọn Thời gian triển khai";
              }
              return null;
            },
            onTap: () async {
              String? provinceCode = state.userType == UserType.personal
                  ? state.customerAddress?.province?.code
                  : state.companyAddress?.province?.code;
              provinceCode ??=
                  GlobalData.instance.addressDefault?.province?.code;

              String? districtCode = state.userType == UserType.personal
                  ? state.customerAddress?.district?.code
                  : state.companyAddress?.district?.code;
              districtCode ??=
                  GlobalData.instance.addressDefault?.district?.code;

              AppBottomSheet.showNormalBottomSheet(
                context,
                title: "Thời gian triển khai",
                isFlexible: true,
                child: SelectDateServiceView(
                  provinceCode: provinceCode,
                  districtCode: districtCode,
                  dateSelected: state.userType == UserType.personal
                      ? state.userTimeSetup
                      : state.companyTimeSetup,
                  isCheckSchedule: state.serviceType == ServiceType.single ||
                      state.serviceType == ServiceType.package,
                  onSelectDateCallback: (date) async {
                    final price = await ref
                        .read(createServiceOrderProvider.notifier)
                        .getPriceOrder(
                          startTime: date.getDate,
                        );

                    if (price != null && price != state.totalTemp) {
                      showDialogConfirmTime(
                        date,
                        price: price,
                      );
                    } else {
                      ref
                          .read(createServiceOrderProvider.notifier)
                          .setUpTime(date);
                    }
                  },
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 20),
        Semantics(
          identifier: "txtNote",
          child: TextFieldWidget.area(
            controller: noteController,
            maxLines: 4,
            maxLength: 2000,
            hintText: "Ghi chú",
            inputFormatters: [
              FilteringTextInputFormatter.allow(
                RegExp(
                  Patterns.note,
                  multiLine: true,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildYearService(ServiceInfoEntity? service) {
    var state = ref.watch(createServiceOrderProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Semantics(
          identifier: "drlSelectPackageService",
          child: DropdownWidget(
            labelText: "Gói dịch vụ",
            isRequired: true,
            content: service?.name ?? "",
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            validator: (value) {
              return ValidateUtils.onValidatePackageCode(
                  state.serviceYearInfo?.code ?? '');
            },
            onTap: () {
              context.push(
                RouterPaths.listPackageService,
                extra: ListPackageServicesArguments(
                  title: "Chọn gói dịch vụ",
                  provinceCode: state.customerSetupAddress?.province?.code,
                  onSelectService: (service) {
                    ref
                        .read(createServiceOrderProvider.notifier)
                        .changeYearService(
                          serviceYearInfo: service,
                        );
                  },
                ),
              );
            },
          ),
        ),
        if (state.serviceYearInfo != null) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              Semantics(
                identifier: "txtBtnDetailPackageService",
                child: AppTextButton(
                  title: "Xem chi tiết gói",
                  titleStyle: UITextStyle.caption1Regular.copyWith(
                    color: BaseColors.info,
                  ),
                  iconRight: MyAssets.icons.iconArrowRightS16Blue.svg(),
                  onTap: () {
                    AppBottomSheet.showNormalBottomSheet(
                      context,
                      title: "Chi tiết gói",
                      height: MediaQuery.of(context).size.height * 0.65,
                      child: YearServiceInfoView(
                        code: service?.code,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildSelectService() {
    var state = ref.watch(createServiceOrderProvider);

    return Semantics(
      identifier: "drlSelectOrderType",
      child: DropdownWidget(
        labelText: "Loại đơn hàng",
        isRequired: true,
        content: state.serviceType?.getTypeName(),
        suffix: MyAssets.icons.iconArrowRightS20.svg(),
        validator: (value) {
          return ValidateUtils.onValidateChoseOrderType(value ?? '');
        },
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());

          AppBottomSheet.showNormalBottomSheet(
            context,
            title: "Loại đơn hàng",
            height: MediaQuery.of(context).size.height * 0.6,
            child: SelectServiceTypeView(
              userType: state.userType,
              serviceTypeSelected: state.serviceType,
              speedOrderType: state.speedOrderType,
              onSelect: (service) {
                bool hasServiceOrSupply = (state.services ?? []).isNotEmpty ||
                    (state.supplies ?? []).isNotEmpty;
                bool isGetBill = state.isGetBill;
                bool hasVoucher = state.voucherSelected != null;
                bool hasPayment = state.paymentSelected != null;

                if (hasServiceOrSupply ||
                    isGetBill ||
                    hasVoucher ||
                    hasPayment) {
                  AppDialog.showDialogConfirm(
                    context,
                    title: "Xác nhận",
                    message:
                        "Chọn lại loại đơn hàng, thông tin dịch vụ, vật tư, hóa đơn & thanh toán sẽ bị xóa. Bạn xác nhận chọn lại loại đơn hàng?",
                    onConfirmAction: () {
                      ref.read(createServiceOrderProvider.notifier).resetInfo();
                      ref
                          .read(createServiceOrderProvider.notifier)
                          .selectServiceType(service);
                    },
                  );
                } else {
                  ref
                      .read(createServiceOrderProvider.notifier)
                      .selectServiceType(service);
                }
              },
            ),
          );
        },
      ),
    );
  }

  void onSelectAddress({
    bool? isSetup,
    AddressInfo? addressInfo,
  }) async {
    AddressEntity? address;
    if (addressInfo != null) {
      address = AddressEntity(
        addressDetail: addressInfo.addressDetail,
        ward: addressInfo.ward,
        district: addressInfo.district,
        province: addressInfo.province,
        isGetFromLocal: addressInfo.isGetFromLocal,
      );
    }

    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        addressSelected: address,
        onSelectAddress: (address) async {
          var state = ref.watch(createServiceOrderProvider);

          if (state.unCheckAddress) {
            ref.read(createServiceOrderProvider.notifier).selectAddress(
                  address.convertToAddressInfo,
                  isSetup: isSetup ?? false,
                );
            return;
          } else {
            validateAddress(
              address: address.convertToAddressInfo,
              isSetup: isSetup,
            );
          }
        },
      ),
    );
  }

  void validateAddress({
    required AddressInfo address,
    int? totalPrice,
    bool? isSetup,
  }) async {
    var state = ref.watch(createServiceOrderProvider);
    AddressInfo? addressSelected;

    if (state.userType == UserType.personal) {
      addressSelected = state.customerSetupAddress;
    } else {
      addressSelected = state.companySetupAddress;
    }

    if (addressSelected == null) {
      if (state.serviceType == ServiceType.single &&
          (address.province?.code !=
                  GlobalData.instance.addressDefault?.province?.code ||
              address.district?.code !=
                  GlobalData.instance.addressDefault?.district?.code)) {
        showAddressDifferentDefault(
          address: address,
          isSetup: isSetup ?? false,
        );
      } else {
        ref.read(createServiceOrderProvider.notifier).selectAddress(
              address,
              isSetup: isSetup ?? false,
            );
      }
    } else {
      ///HiSmile bo sung logic check ma giam gia, check tai tho
      int? price;
      if (totalPrice != null) {
        price = totalPrice;
      } else {
        price =
            await ref.read(createServiceOrderProvider.notifier).getPriceOrder(
                  provinceCode: address.province?.code,
                  districtCode: address.district?.code,
                );
      }

      bool check = (state.voucherSelected != null) ||
          (price != state.totalTemp) ||
          (address.province?.code != addressSelected.province?.code ||
              address.district?.code != addressSelected.district?.code);

      if (check) {
        showDialogChangeAddress(
          address: address,
          price: price,
          isSetup: isSetup ?? false,
        );
      } else {
        ref.read(createServiceOrderProvider.notifier).selectAddress(
              address,
              isSetup: isSetup ?? false,
            );
      }
    }
  }

  void checkStaff({
    String? phoneNumber,
    required BuildContext context,
  }) async {
    final isStaff =
        await ref.read(createServiceOrderProvider.notifier).checkInternalStaff(
              phoneNumber: phoneNumber,
            );

    final isFT3 = await ref
        .read(createServiceOrderProvider.notifier)
        .checkRoleFT3(phoneNumber: phoneNumber);

    if (isFT3 ?? false) {
      if (!context.mounted) return;
      await AppDialog.showDialogInfo(
        context,
        barrierDismissible: false,
        message:
            "Bạn không được phép dùng số điện thoại của FT3 để lên đơn hàng, vui lòng nhập đúng số điện thoai của khách hàng",
        onConfirmAction: () {
          userPhoneNumber.clear();
          userPhoneNumberSetup.clear();
        },
      );
      return;
    }

    if (isStaff ?? false) {
      if (!context.mounted) return;
      AppDialog.showDialogConfirm(
        context,
        title: "Xác nhận",
        message:
            "Số điện thoại của nhân viên nội bộ, bạn có chắc chắn tạo đơn hàng không?",
        buttonNameConfirm: "Tạo đơn",
        onConfirmAction: () {
          FocusScope.of(context).requestFocus(FocusNode());

          getCustomerInfo360(
            phoneNumber: phoneNumber,
            checkPrice: false,
            isInternalStaff: true,
            onSuccess: (customerInfo) {
              userName.text = customerInfo.fullName ?? '';
              userPhoneNumberSetup.text = customerInfo.phone ?? '';
              userNameSetup.text = customerInfo.fullName ?? '';
            },
            onFailure: () {
              ref
                  .read(createServiceOrderProvider.notifier)
                  .enableEditUserForm();
            },
          );
        },
        onCancelAction: () {
          userPhoneNumber.clear();
          userPhoneNumberSetup.clear();
        },
      );
    } else {
      getCustomerInfo360(
        phoneNumber: phoneNumber,
        checkPrice: false,
        onSuccess: (customerInfo) {
          userName.text = customerInfo.fullName ?? '';
          userPhoneNumberSetup.text = customerInfo.phone ?? '';
          userNameSetup.text = customerInfo.fullName ?? '';
        },
        onFailure: () {
          ref.read(createServiceOrderProvider.notifier).enableEditUserForm();
        },
      );
    }
  }

  void checkStaffContactCompany({
    String? phoneNumber,
    required BuildContext context,
  }) async {
    final isStaff =
        await ref.read(createServiceOrderProvider.notifier).checkInternalStaff(
              phoneNumber: phoneNumber,
            );

    if (isStaff ?? false) {
      if (!context.mounted) return;
      AppDialog.showDialogConfirm(
        context,
        title: "Xác nhận",
        message:
            "Số điện thoại của nhân viên nội bộ, bạn có chắc chắn tạo đơn hàng không?",
        buttonNameConfirm: "Tạo đơn",
        onConfirmAction: () {
          getCustomerInfo360(
            phoneNumber: phoneNumber,
            isInternalStaff: true,
            isCompanyType: true,
            checkPrice: false,
            onSuccess: (customerInfo) {
              companyUserName.text = customerInfo.fullName ?? '';
              companyUserNameSetup.text = customerInfo.fullName ?? '';
            },
            onFailure: () {
              ref
                  .read(createServiceOrderProvider.notifier)
                  .enableEditCompanyForm();
            },
          );
        },
        onCancelAction: () {
          companyUserPhoneNumber.clear();
          companyUserPhoneNumberSetup.clear();
        },
      );
    } else {
      getCustomerInfo360(
        phoneNumber: phoneNumber,
        checkPrice: false,
        isCompanyType: true,
        onSuccess: (customerInfo) {
          companyUserName.text = customerInfo.fullName ?? '';
          companyUserNameSetup.text = customerInfo.fullName ?? '';
        },
        onFailure: () {
          ref.read(createServiceOrderProvider.notifier).enableEditCompanyForm();
        },
      );
    }
  }

  Future<void> getCustomerInfo360({
    String? phoneNumber,
    bool? isInternalStaff,
    bool? checkPrice,
    bool? isCompanyType,
    Function? onSuccess,
    Function? onFailure,
  }) async {
    if ((phoneNumber ?? '').validatePhone()) {
      final customerInfo = await ref
          .read(createServiceOrderProvider.notifier)
          .getCustomerInfo360(
            phoneNumber: phoneNumber,
            isCompanyType: isCompanyType,
            isInternalStaff: isInternalStaff ?? false,
            checkPrice: checkPrice ?? true,
          );

      if (customerInfo != null) {
        onSuccess?.call(customerInfo);
      }
    } else {
      onFailure?.call();
    }
  }

  void getCompanyInfo360({
    String? taxCode,
  }) async {
    if ((taxCode ?? '').validateTax()) {
      final customerInfo =
          await ref.read(createServiceOrderProvider.notifier).getCompanyInfo360(
                taxCode: taxCode,
              );

      if (customerInfo != null) {
        companyName.text = customerInfo.fullName ?? '';
        companyPhoneNumber.text = customerInfo.phone ?? '';
      } else {
        companyName.text = "";
      }
    } else {
      ref.read(createServiceOrderProvider.notifier).enableEditCompanyForm();
      companyName.text = "";
    }
  }

  void onSaveInfo() async {
    bool isPersonalType =
        ref.watch(createServiceOrderProvider).userType == UserType.personal;

    if (isPersonalType) {
      final form = formUserKey.currentState;
      final formSetup = formUserSetupKey.currentState;

      if (form!.validate() && formSetup!.validate()) {
        form.save();
        onNextPage(context);
      } else {
        ref.read(createServiceOrderProvider.notifier).onValidateUserForm();
      }
    } else {
      final form = formCompanyKey.currentState;
      final formSetup = formCompanySetupKey.currentState;

      if (form!.validate() && formSetup!.validate()) {
        form.save();
        onNextPage(context);
      } else {
        ref.read(createServiceOrderProvider.notifier).onValidateCompanyForm();
      }
    }
  }

  void onNextPage(BuildContext context) async {
    final state = ref.watch(createServiceOrderProvider);
    bool isPersonalType = state.userType == UserType.personal;
    final userInfo = isPersonalType
        ? CustomerInfoParam(
            customerType: UserType.personal.keyToServer,
            customerId: userPhoneNumber.text,
            phoneNumber: userPhoneNumber.text,
            fullName: userName.text,
            gender: state.genderType.keyToServer,
            address: state.customerAddress?.getFullAddress,
            addressDetail: state.customerAddress?.addressDetail,
            wardCode: state.customerAddress?.ward?.code,
            districtCode: state.customerAddress?.district?.code,
            provinceCode: state.customerAddress?.province?.code,
          )
        : CustomerInfoParam(
            customerType: state.userType.keyToServer,
            customerId: taxCompany.text,
            fullName: companyName.text,
            phoneNumber: companyPhoneNumber.text,
            contactName: companyUserName.text,
            contactNumber: companyUserPhoneNumber.text,
            address: state.companyAddress?.getFullAddress,
            addressDetail: state.companyAddress?.addressDetail,
            wardCode: state.companyAddress?.ward?.code,
            districtCode: state.companyAddress?.district?.code,
            provinceCode: state.companyAddress?.province?.code,
          );

    final shippingInfo = isPersonalType
        ? ShippingInfoParam(
            fullName: userNameSetup.text,
            phoneNumber: userPhoneNumberSetup.text,
            startTime: state.userTimeSetup?.getDateFrom.toIso8601String(),
            endTime: state.userTimeSetup?.getDateTo.toIso8601String(),
            note: noteController.text,
            address: state.customerSetupAddress?.getFullAddress,
            addressDetail: state.customerSetupAddress?.addressDetail,
            wardCode: state.customerSetupAddress?.ward?.code,
            districtCode: state.customerSetupAddress?.district?.code,
            provinceCode: state.customerSetupAddress?.province?.code,
          )
        : ShippingInfoParam(
            fullName: companyUserNameSetup.text,
            phoneNumber: companyUserPhoneNumberSetup.text,
            startTime: state.companyTimeSetup?.getDateFrom.toIso8601String(),
            endTime: state.companyTimeSetup?.getDateTo.toIso8601String(),
            note: noteController.text,
            address: state.companySetupAddress?.getFullAddress,
            addressDetail: state.companySetupAddress?.addressDetail,
            wardCode: state.companySetupAddress?.ward?.code,
            districtCode: state.companySetupAddress?.district?.code,
            provinceCode: state.companySetupAddress?.province?.code,
          );

    final productOrder = OrderParam(
      orderType: state.serviceType.keyToCreateOrder,
      userInfo: userInfo,
      shippingInfo: shippingInfo,
      servicesInfo: state.serviceYearInfo != null
          ? [state.serviceYearInfo!]
          : state.services,
      packageCode: state.serviceYearInfo?.code,
      isSpeedService: state.isSetupSpeedService,
    );

    final result =
        await ref.read(createServiceOrderProvider.notifier).checkExistOrder(
              customerId: productOrder.userInfo?.customerId ?? '',
            );

    if (result ?? false) {
      if (!context.mounted) return;
      AppBottomSheet.showNormalBottomSheet(
        context,
        title: "Xác nhận",
        height: MediaQuery.of(context).size.height * 0.95,
        child: VerifyServiceOrderView(
          orders: ref.watch(createServiceOrderProvider).listOrderExist ?? [],
          onConfirm: () async {
            ref.read(createServiceOrderProvider.notifier).saveOrderInfo(
                  productOrder,
                );
            widget.onNextPageCallBack?.call();
          },
        ),
      );
    } else {
      ref.read(createServiceOrderProvider.notifier).saveOrderInfo(
            productOrder,
          );
      widget.onNextPageCallBack?.call();
    }
  }

  Widget _buildContentCommissionConfirm({
    String? message,
    List<UserRateEntity>? userRates,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(
          message ?? "",
          style: UITextStyle.body2Regular.copyWith(
            color: BaseColors.textBody,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        if ((userRates ?? []).isNotEmpty) ...[
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: BaseColors.borderDefault,
              ),
            ),
            child: ListView.separated(
              itemCount: userRates?.length ?? 0,
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (_, __) {
                return const DividerWidget();
              },
              itemBuilder: (context, index) {
                final item = userRates?[index];
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          item?.title ?? '',
                          style: UITextStyle.body2Regular.copyWith(
                            color: BaseColors.textBody,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        "${item?.percent ?? 0}",
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }
}
