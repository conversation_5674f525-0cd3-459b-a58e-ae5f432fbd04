import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/enums/data_type.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/domain/enums/special_service_code.dart';
import 'package:vcc/domain/params/product_order_param.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/bottom_sheet/change_service/change_service_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/add_supplies/add_supplies_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/create_service_order_view_model.dart';
import 'package:vcc/presentation/views/features/order/detail_order/add_supply_combo_sale_point/add_supply_sale_point_combo_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/add_supply_sale_point/add_supplies_sale_point_page.dart';
import 'package:vcc/presentation/views/features/store/service/single_service/single_service_page.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/plus_and_minus_widget.dart';
import 'package:vcc/utils/string_utils.dart';

class ServiceOrderInfoPage extends StatefulHookConsumerWidget {
  final Function? onNextPageCallBack;

  const ServiceOrderInfoPage({
    super.key,
    this.onNextPageCallBack,
  });

  @override
  ConsumerState<ServiceOrderInfoPage> createState() =>
      _ServiceOrderInfoPageState();
}

class _ServiceOrderInfoPageState extends ConsumerState<ServiceOrderInfoPage> {
  @override
  void initState() {
    Future(() {
      ref.read(createServiceOrderProvider.notifier).setupService();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(createServiceOrderProvider);
    bool isDifferentSupplyType = ![
      ServiceType.supply,
      ServiceType.salePoint,
      ServiceType.salePointCombo,
    ].contains(state.serviceType);

    return Semantics(
      identifier: "serviceOrderInfoContainer",
      child: Container(
        color: BaseColors.backgroundGray,
        child: Stack(
          children: [
            Semantics(
              identifier: "serviceOrderInfoContent",
              child: ListView(
                padding: const EdgeInsets.only(bottom: 160),
                children: <Widget>[
              if (isDifferentSupplyType) ...[
                Container(
                  color: BaseColors.backgroundWhite,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ),
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        child: Text(
                          "Danh sách dịch vụ",
                          style: UITextStyle.body1SemiBold.copyWith(
                            color: BaseColors.textTitle,
                          ),
                        ),
                      ),
                      Semantics(
                        identifier: "txtBtnAddService",
                        child: AppTextButton(
                          iconLeft: MyAssets.icons.iconAddCircle.svg(),
                          title: "Thêm dịch vụ",
                          onTap: () {
                            addService();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                if ((state.services?.length ?? 0) > 0) ...[
                  Container(
                    color: BaseColors.backgroundWhite,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                    ),
                    child: const DividerWidget(),
                  ),
                  Container(
                    color: BaseColors.backgroundWhite,
                    child: Semantics(
                      identifier: "servicesList",
                      child: ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: const EdgeInsets.only(bottom: 16),
                        itemCount: state.services?.length ?? 0,
                        separatorBuilder: (_, __) {
                          return const Padding(
                            padding: EdgeInsets.only(left: 16),
                            child: DividerWidget(),
                          );
                        },
                        itemBuilder: (context, index) {
                          final item = state.services![index];
                          return _buildServiceItem(
                            index: index,
                            service: item,
                          );
                        },
                      ),
                    ),
                  ),
                ],
                const SizedBox(
                  height: 8,
                  width: double.infinity,
                ),
              ],
              Container(
                color: BaseColors.backgroundWhite,
                padding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: isDifferentSupplyType ? 10 : 0,
                ),
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: Text(
                        "Danh sách vật tư",
                        style: UITextStyle.body1SemiBold.copyWith(
                          color: BaseColors.textTitle,
                        ),
                      ),
                    ),
                    Visibility(
                      visible: state.serviceType != ServiceType.package,
                      child: Semantics(
                        identifier: "txtBtnAddSupply",
                        child: AppTextButton(
                          iconLeft: MyAssets.icons.iconAddCircle.svg(),
                          title: "Thêm vật tư",
                          onTap: addSupplies,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if ((state.supplies?.length ?? 0) > 0) ...[
                Container(
                  color: BaseColors.backgroundWhite,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ),
                  child: const DividerWidget(),
                ),
                Container(
                  color: BaseColors.backgroundWhite,
                  child: Semantics(
                    identifier: "suppliesList",
                    child: ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.only(bottom: 16),
                      itemCount: state.supplies?.length ?? 0,
                      separatorBuilder: (_, __) {
                        return const Padding(
                          padding: EdgeInsets.only(left: 16),
                          child: DividerWidget(),
                        );
                      },
                      itemBuilder: (context, index) {
                        final item = state.supplies![index];
                      return _buildItemSupply(
                        index: index,
                        supply: item,
                      );
                    },
                  ),
                ),
                ),
              ],
            ],
          ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            left: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              decoration: BoxDecoration(
                color: BaseColors.backgroundWhite,
                boxShadow: AppBoxShadows.shadowNormal,
              ),
              child: SafeArea(
                top: false,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 10,
                      ),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Text(
                              "Tổng tiền tạm tính",
                              style: UITextStyle.body2Regular.copyWith(
                                color: BaseColors.textLabel,
                              ),
                            ),
                          ),
                          Text(
                            StringUtils.formatMoney(state.totalAmount ?? 0),
                            style: UITextStyle.body1SemiBold.copyWith(
                              color: BaseColors.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                      ),
                      child: Semantics(
                        identifier: "btnNextStepThree",
                        child: BaseButton(
                          text: "Tiếp tục",
                          onTap: () {
                            if (state.serviceType == ServiceType.supply ||
                                state.serviceType == ServiceType.salePoint ||
                                state.serviceType ==
                                    ServiceType.salePointCombo) {
                              if ((state.supplies ?? []).isEmpty) {
                                AppDialog.showDialogInfo(
                                  context,
                                  title: "Thông báo",
                                  message: "Vui lòng chọn ít nhất 1 vật tư",
                                  buttonNameConfirm: "Đóng",
                                );
                                return;
                              }
                            } else {
                              if ((state.services ?? []).isEmpty ||
                                  (state.isSetupSpeedService &&
                                      (state.services?.length ?? 0) <= 1)) {
                                AppDialog.showDialogInfo(
                                  context,
                                  title: "Thông báo",
                                  message: "Vui lòng chọn ít nhất 1 dịch vụ",
                                  buttonNameConfirm: "Đóng",
                                );
                                return;
                              }
                            }

                            final orderInfo = OrderParam(
                              servicesInfo: state.services ?? [],
                              suppliesInfo: state.supplies ?? [],
                              packageCode: state.serviceOrderInfo?.packageCode,
                            );

                            ref
                                .read(createServiceOrderProvider.notifier)
                                .saveOrderInfo(orderInfo);
                            ref
                                .read(createServiceOrderProvider.notifier)
                                .calculatorPriceOrder(
                                  phoneNumber: state
                                      .serviceOrderInfo?.userInfo?.phoneNumber,
                                );
                            widget.onNextPageCallBack?.call();
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceItem({
    required ServiceInfoEntity service,
    required int index,
  }) {
    bool disableChangeQuantity = [
      SpecialServiceCode.supportService.codeToServer,
      SpecialServiceCode.supportServiceUnlimited.codeToServer,
      SpecialServiceCode.hotService.codeToServer,
    ].contains(service.code);

    return Semantics(
      identifier: "serviceItem_${service.code ?? index}",
      child: Slidable(
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          extentRatio: 0.2,
          children: <Widget>[
          CustomSlidableAction(
            backgroundColor: BaseColors.primary,
            foregroundColor: Colors.yellow,
            child: Semantics(
              identifier: "btnRemoveService",
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  MyAssets.icons.iconWhiteTrash.svg(),
                  const SizedBox(height: 8),
                  Text(
                    "Xoá",
                    style: UITextStyle.body2SemiBold.copyWith(
                      color: BaseColors.backgroundWhite,
                    ),
                  ),
                ],
              ),
            ),
            onPressed: (context) {
              if ((service.isRequired ?? false) || disableChangeQuantity) {
                AppDialog.showDialogInfo(
                  context,
                  barrierDismissible: false,
                  title: "Thông báo",
                  message: "Dịch vụ mặc định không thể xóa",
                  buttonNameConfirm: "Đóng",
                  onConfirmAction: () {
                    ref
                        .read(createServiceOrderProvider.notifier)
                        .changeQuantity(
                          indexItem: index,
                          quantity: 1,
                        );
                  },
                );
              } else {
                showDialogRemoveService(
                  index: index,
                  serviceName: service.name ?? "",
                );
              }
            },
          )
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: <Widget>[
                Container(
                  height: 56,
                  width: 56,
                  decoration: BoxDecoration(
                    color: BaseColors.backgroundGray,
                  ),
                  child: ImageWidget(
                    service.thumbnailUrl ??
                        BaseConstant.imageDefaultServiceTest,
                    size: const Size(56, 56),
                  ),
                ),
                const SizedBox(width: 14),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        service.name ?? '',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 4,
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  RichText(
                                    text: TextSpan(
                                      text: StringUtils.formatMoney(
                                          service.price ?? 0),
                                      style: UITextStyle.body2Medium.copyWith(
                                        color: BaseColors.primary,
                                      ),
                                      children: <TextSpan>[
                                        TextSpan(
                                          text: "/${service.unit ?? ''}",
                                          style:
                                              UITextStyle.body2Regular.copyWith(
                                            color: BaseColors.textSubtitle,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  if ((service.discount ?? 0) > 0) ...[
                                    const SizedBox(height: 2),
                                    Row(
                                      children: [
                                        Text(
                                          StringUtils.formatMoney(
                                            service.originalPrice ?? 0,
                                          ),
                                          style: UITextStyle.caption1Regular
                                              .copyWith(
                                            color: BaseColors.textSubtitle,
                                            decoration:
                                                TextDecoration.lineThrough,
                                          ),
                                        ),
                                        const SizedBox(width: 4),
                                        Container(
                                          height: 20,
                                          decoration: BoxDecoration(
                                            color: BaseColors.primarySurface,
                                            borderRadius:
                                                BorderRadius.circular(6),
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 1,
                                            horizontal: 4,
                                          ),
                                          child: Center(
                                            child: Text(
                                              '-${service.discount?.toInt() ?? 0}%',
                                              style: UITextStyle.caption1Medium
                                                  .copyWith(
                                                color: BaseColors.primary,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                          PlusAndMinusWidget(
                            quantity: service.quantity,
                            minQuantity: service.minItem,
                            maxQuantity: service.maxItem,
                            step: service.step,
                            dataType: service.valueType,
                            onPlus: (value) {
                              ref
                                  .read(createServiceOrderProvider.notifier)
                                  .changeQuantity(
                                    indexItem: index,
                                    quantity: value,
                                  );
                            },
                            onMinus: (value) {
                              if (value == 0) {
                                if (service.isRequired ?? false) {
                                  AppDialog.showDialogInfo(
                                    context,
                                    barrierDismissible: false,
                                    title: "Thông báo",
                                    message: "Dịch vụ mặc định không thể xóa",
                                    buttonNameConfirm: "Đóng",
                                    onConfirmAction: () {
                                      ref
                                          .read(createServiceOrderProvider
                                              .notifier)
                                          .changeQuantity(
                                            indexItem: index,
                                            quantity: 1,
                                          );
                                    },
                                  );
                                } else {
                                  showDialogRemoveService(
                                    index: index,
                                    serviceName: service.name ?? "",
                                  );
                                }
                              } else {
                                ref
                                    .read(createServiceOrderProvider.notifier)
                                    .changeQuantity(
                                      indexItem: index,
                                      quantity: value,
                                    );
                              }
                            },
                            disableChangeQuantity: disableChangeQuantity,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: RichText(
                text: TextSpan(
                  text: 'Thành tiền: ',
                  style: UITextStyle.caption1Medium.copyWith(
                    color: BaseColors.textSubtitle,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: StringUtils.formatMoney(
                          service.quantity * (service.price ?? 0)),
                      style: UITextStyle.caption1SemiBold.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (service.swapCode != null) ...[
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.centerRight,
                child: Semantics(
                  identifier: "btnChangeService",
                  child: InkWellWidget(
                    onTap: () {
                      changeService(service);
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        MyAssets.icons.iconExchange.svg(),
                        const SizedBox(width: 8),
                        Text(
                          "Đổi dịch vụ",
                          style: UITextStyle.body2Medium.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    ),
    );
  }

  Widget _buildItemSupply({
    required SupplyEntity supply,
    required int index,
  }) {
    return Semantics(
      identifier: "supplyItem_${supply.code ?? index}",
      child: Slidable(
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          extentRatio: 0.2,
          children: <Widget>[
          CustomSlidableAction(
            backgroundColor: BaseColors.primary,
            foregroundColor: Colors.yellow,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                MyAssets.icons.iconWhiteTrash.svg(),
                const SizedBox(height: 8),
                Text(
                  "Xoá",
                  style: UITextStyle.body2SemiBold.copyWith(
                    color: BaseColors.backgroundWhite,
                  ),
                ),
              ],
            ),
            onPressed: (context) {
              if (supply.isRequired ?? false) {
                AppDialog.showDialogInfo(
                  context,
                  barrierDismissible: false,
                  title: "Thông báo",
                  message: "Vật tư mặc định không thể xóa",
                  buttonNameConfirm: "Đóng",
                  onConfirmAction: () {
                    ref
                        .read(createServiceOrderProvider.notifier)
                        .changeSupplyQuantity(
                          indexItem: index,
                          quantity: 1,
                        );
                  },
                );
              } else {
                showDialogRemoveSupply(
                  index: index,
                  supplyName: supply.name ?? "",
                );
              }
            },
          )
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: <Widget>[
                MyAssets.icons.iconSupply.svg(),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        supply.name ?? '',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                RichText(
                                  text: TextSpan(
                                    text: StringUtils.formatMoney(
                                        supply.price ?? 0),
                                    style: UITextStyle.body2Medium.copyWith(
                                      color: BaseColors.primary,
                                    ),
                                    children: <TextSpan>[
                                      TextSpan(
                                        text: "/${supply.unit ?? ''}",
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Visibility(
                                  visible: supply.originalPrice != null,
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: RichText(
                                      text: TextSpan(
                                        style: UITextStyle.caption1Regular
                                            .copyWith(
                                          color: BaseColors.textBody,
                                        ),
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: "Giá vốn",
                                            style: UITextStyle.caption1Regular
                                                .copyWith(
                                              color: BaseColors.textSubtitle,
                                            ),
                                          ),
                                          TextSpan(
                                            text:
                                                ' ${StringUtils.formatMoney(supply.originalPrice ?? 0)}',
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible: supply.supplyType != null,
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: RichText(
                                      text: TextSpan(
                                        style: UITextStyle.caption1Regular
                                            .copyWith(
                                          color: BaseColors.textBody,
                                        ),
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: "Loại vật tư",
                                            style: UITextStyle.caption1Regular
                                                .copyWith(
                                              color: BaseColors.textSubtitle,
                                            ),
                                          ),
                                          TextSpan(
                                            text: ' ${supply.getWmsName}',
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          PlusAndMinusWidget(
                            quantity: supply.quantity,
                            minQuantity: supply.minItem,
                            maxQuantity: supply.maxItem,
                            step: supply.step,
                            dataType: DataType.double,
                            onPlus: (value) {
                              ref
                                  .read(createServiceOrderProvider.notifier)
                                  .changeSupplyQuantity(
                                    indexItem: index,
                                    quantity: value,
                                  );
                            },
                            onMinus: (value) {
                              if (value == 0) {
                                if (supply.isRequired ?? false) {
                                  AppDialog.showDialogInfo(
                                    context,
                                    barrierDismissible: false,
                                    title: "Thông báo",
                                    message: "Vật tư mặc định không thể xóa",
                                    buttonNameConfirm: "Đóng",
                                    onConfirmAction: () {
                                      ref
                                          .read(createServiceOrderProvider
                                              .notifier)
                                          .changeSupplyQuantity(
                                            indexItem: index,
                                            quantity: 1,
                                          );
                                    },
                                  );
                                } else {
                                  showDialogRemoveSupply(
                                    index: index,
                                    supplyName: supply.name ?? "",
                                  );
                                }
                              } else {
                                ref
                                    .read(createServiceOrderProvider.notifier)
                                    .changeSupplyQuantity(
                                      indexItem: index,
                                      quantity: value,
                                    );
                              }
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: RichText(
                text: TextSpan(
                  text: 'Thành tiền: ',
                  style: UITextStyle.caption1Medium.copyWith(
                    color: BaseColors.textSubtitle,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: StringUtils.formatMoney(
                        supply.quantity * (supply.price ?? 0),
                      ),
                      style: UITextStyle.caption1SemiBold.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    ),
    );
  }

  void showDialogRemoveService({
    required int index,
    required String serviceName,
  }) {
    AppDialog.showDialogConfirm(
      context,
      title: "Xác nhận",
      message: 'Bạn có chắc chắn muốn xóa dịch vụ "$serviceName"?',
      buttonNameConfirm: "Xác nhận",
      onConfirmAction: () {
        ref.read(createServiceOrderProvider.notifier).changeQuantity(
              indexItem: index,
              quantity: 0,
            );
      },
      onCancelAction: () {
        ref.read(createServiceOrderProvider.notifier).changeQuantity(
              indexItem: index,
              quantity: 1,
            );
      },
    );
  }

  void showDialogRemoveSupply({
    required int index,
    required String supplyName,
  }) {
    AppDialog.showDialogConfirm(
      context,
      barrierDismissible: false,
      title: "Xác nhận",
      message: 'Bạn có chắc chắn muốn xóa vật tư "$supplyName"?',
      buttonNameConfirm: "Xác nhận",
      onConfirmAction: () {
        ref.read(createServiceOrderProvider.notifier).changeSupplyQuantity(
              indexItem: index,
              quantity: 0,
            );
      },
      onCancelAction: () {
        ref.read(createServiceOrderProvider.notifier).changeSupplyQuantity(
              indexItem: index,
              quantity: 1,
            );
      },
    );
  }

  void addService() async {
    var state = ref.watch(createServiceOrderProvider);
    if ([
      ServiceType.single,
      ServiceType.combo,
      ServiceType.package,
    ].contains(state.serviceType)) {
      final services = await context.push(
        RouterPaths.listSingleService,
        extra: SingleServicesArguments(
          services: state.services,
          isAddService: true,
          startDate: state.serviceOrderInfo?.shippingInfo?.startTime,
          provinceCode: state.serviceOrderInfo?.shippingInfo?.provinceCode,
          districtCode: state.serviceOrderInfo?.shippingInfo?.districtCode,
          serviceType: state.serviceType,
        ),
      );

      if (services is List<ServiceInfoEntity>) {
        ref.read(createServiceOrderProvider.notifier).changeService(services);
      }
    }
  }

  void addSupplies() async {
    var state = ref.watch(createServiceOrderProvider);

    bool canChooseFullSupplies = [
      ServiceType.single,
      ServiceType.combo,
      ServiceType.supply,
    ].contains(state.serviceType);

    if (canChooseFullSupplies) {
      final supplies = await context.push(
        RouterPaths.addSupplies,
        extra: AddSuppliesArguments(
          listSupplySelected: state.supplies,
        ),
      );

      if (supplies is List<SupplyEntity>) {
        ref.read(createServiceOrderProvider.notifier).changeSupplies(
              supplies,
            );
      }
    } else if (state.serviceType == ServiceType.salePoint) {
      var supplies = await context.push(
        RouterPaths.addSuppliesSalePoint,
        extra: AddSuppliesSalePointArguments(),
      );

      if (supplies is List<SupplyEntity>) {
        ref.read(createServiceOrderProvider.notifier).changeSupplies(
              supplies,
            );
      }
    } else if (state.serviceType == ServiceType.salePointCombo) {
      context.push(
        RouterPaths.addSuppliesSalePointCombo,
        extra: AddSuppliesSalePointComboArguments(
          onSelectService: (service) {
            ref.read(createServiceOrderProvider.notifier).getServiceInfo(
                  code: service.code ?? "",
                );
          },
        ),
      );
    }
  }

  void changeService(ServiceInfoEntity service) async {
    final result = await AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Đổi dịch vụ trong gói",
      height: MediaQuery.of(context).size.height * 0.6,
      child: ChangeServiceView(
        swapCode: service.swapCode ?? '',
      ),
    );

    if (result != null) {
      final listService = ref.read(createServiceOrderProvider).services;
      final index = listService?.indexWhere((element) => element == service);

      if (index != null && index >= 0) {
        listService![index] = result;
        ref
            .read(createServiceOrderProvider.notifier)
            .changeService(listService);
      }
    }
  }
}
